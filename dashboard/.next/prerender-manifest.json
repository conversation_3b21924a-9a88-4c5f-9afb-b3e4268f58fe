{"version": 4, "routes": {"/api/policies": {"initialHeaders": {"content-type": "application/json", "x-next-cache-tags": "_N_T_/layout,_N_T_/api/layout,_N_T_/api/policies/layout,_N_T_/api/policies/route,_N_T_/api/policies"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/api/policies", "dataRoute": null}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "c0e4ca1542845aa036c74859eb82c4ea", "previewModeSigningKey": "e0532d3a269df5a77ce2f27211bfcd55d27b9b044da6c3d80ae517bf78b1c20e", "previewModeEncryptionKey": "f2b1c554e70949a144380fd50a2364b6cd280d4274445d5503ac4b19c18ec97c"}}