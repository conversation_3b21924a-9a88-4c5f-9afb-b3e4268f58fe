/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.visible {
  visibility: visible;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.bottom-0 {
  bottom: 0px;
}
.left-0 {
  left: 0px;
}
.left-1 {
  left: 0.25rem;
}
.left-1\/2 {
  left: 50%;
}
.left-2 {
  left: 0.5rem;
}
.right-0 {
  right: 0px;
}
.right-1 {
  right: 0.25rem;
}
.right-1\/2 {
  right: 50%;
}
.top-0 {
  top: 0px;
}
.-z-10 {
  z-index: -10;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-50 {
  z-index: 50;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.col-span-10 {
  grid-column: span 10 / span 10;
}
.col-span-11 {
  grid-column: span 11 / span 11;
}
.col-span-12 {
  grid-column: span 12 / span 12;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.col-span-3 {
  grid-column: span 3 / span 3;
}
.col-span-4 {
  grid-column: span 4 / span 4;
}
.col-span-5 {
  grid-column: span 5 / span 5;
}
.col-span-6 {
  grid-column: span 6 / span 6;
}
.col-span-7 {
  grid-column: span 7 / span 7;
}
.col-span-8 {
  grid-column: span 8 / span 8;
}
.col-span-9 {
  grid-column: span 9 / span 9;
}
.mx-2\.5 {
  margin-left: 0.625rem;
  margin-right: 0.625rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.-mb-px {
  margin-bottom: -1px;
}
.-ml-0 {
  margin-left: -0px;
}
.-ml-0\.5 {
  margin-left: -0.125rem;
}
.-ml-1 {
  margin-left: -0.25rem;
}
.-ml-1\.5 {
  margin-left: -0.375rem;
}
.-ml-px {
  margin-left: -1px;
}
.-mr-1 {
  margin-right: -0.25rem;
}
.mb-0 {
  margin-bottom: 0px;
}
.mb-1\.5 {
  margin-bottom: 0.375rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-1\.5 {
  margin-left: 0.375rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-px {
  margin-left: 1px;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-1\.5 {
  margin-right: 0.375rem;
}
.mr-10 {
  margin-right: 2.5rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-2\.5 {
  margin-right: 0.625rem;
}
.mr-20 {
  margin-right: 5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mr-5 {
  margin-right: 1.25rem;
}
.mr-8 {
  margin-right: 2rem;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-4 {
  margin-top: 1rem;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.size-3\.5 {
  width: 0.875rem;
  height: 0.875rem;
}
.h-0 {
  height: 0px;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-2 {
  height: 0.5rem;
}
.h-3 {
  height: 0.75rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-4 {
  height: 1rem;
}
.h-40 {
  height: 10rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-7 {
  height: 1.75rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[1px\] {
  height: 1px;
}
.h-full {
  height: 100%;
}
.max-h-\[228px\] {
  max-height: 228px;
}
.min-h-full {
  min-height: 100%;
}
.min-h-screen {
  min-height: 100vh;
}
.w-0 {
  width: 0px;
}
.w-1 {
  width: 0.25rem;
}
.w-1\/2 {
  width: 50%;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-28 {
  width: 7rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-4 {
  width: 1rem;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-7 {
  width: 1.75rem;
}
.w-9 {
  width: 2.25rem;
}
.w-\[90\%\] {
  width: 90%;
}
.w-\[var\(--button-width\)\] {
  width: var(--button-width);
}
.w-full {
  width: 100%;
}
.w-max {
  width: -moz-max-content;
  width: max-content;
}
.w-screen {
  width: 100vw;
}
.min-w-44 {
  min-width: 11rem;
}
.min-w-\[10rem\] {
  min-width: 10rem;
}
.min-w-min {
  min-width: -moz-min-content;
  min-width: min-content;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-\[100px\] {
  max-width: 100px;
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-none {
  flex: none;
}
.shrink-0 {
  flex-shrink: 0;
}
.border-collapse {
  border-collapse: collapse;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-4 {
  --tw-translate-y: -1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1\/2 {
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-5 {
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-180 {
  --tw-rotate: -180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-90 {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize {
  resize: both;
}
.snap-mandatory {
  --tw-scroll-snap-strictness: mandatory;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-10 {
  grid-template-columns: repeat(10, minmax(0, 1fr));
}
.grid-cols-11 {
  grid-template-columns: repeat(11, minmax(0, 1fr));
}
.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}
.grid-cols-9 {
  grid-template-columns: repeat(9, minmax(0, 1fr));
}
.grid-cols-none {
  grid-template-columns: none;
}
.flex-row {
  flex-direction: row;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.flex-col {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.items-baseline {
  align-items: baseline;
}
.items-stretch {
  align-items: stretch;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}
.justify-evenly {
  justify-content: space-evenly;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-x-1 {
  -moz-column-gap: 0.25rem;
       column-gap: 0.25rem;
}
.space-x-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.125rem * var(--tw-space-x-reverse));
  margin-left: calc(0.125rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.375rem * var(--tw-space-x-reverse));
  margin-left: calc(0.375rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-tremor-border > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-visible {
  overflow: visible;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-clip {
  overflow-x: clip;
}
.overflow-x-scroll {
  overflow-x: scroll;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.\!rounded-none {
  border-radius: 0px !important;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-\[1px\] {
  border-radius: 1px;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-l-none {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.rounded-r-none {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}
.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-b-4 {
  border-bottom-width: 4px;
}
.border-l {
  border-left-width: 1px;
}
.border-l-4 {
  border-left-width: 4px;
}
.border-r {
  border-right-width: 1px;
}
.border-r-4 {
  border-right-width: 4px;
}
.border-t-4 {
  border-top-width: 4px;
}
.border-t-\[1px\] {
  border-top-width: 1px;
}
.border-dashed {
  border-style: dashed;
}
.border-none {
  border-style: none;
}
.border-amber-100 {
  --tw-border-opacity: 1;
  border-color: rgb(254 243 199 / var(--tw-border-opacity, 1));
}
.border-amber-200 {
  --tw-border-opacity: 1;
  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));
}
.border-amber-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 211 77 / var(--tw-border-opacity, 1));
}
.border-amber-400 {
  --tw-border-opacity: 1;
  border-color: rgb(251 191 36 / var(--tw-border-opacity, 1));
}
.border-amber-50 {
  --tw-border-opacity: 1;
  border-color: rgb(255 251 235 / var(--tw-border-opacity, 1));
}
.border-amber-500 {
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}
.border-amber-600 {
  --tw-border-opacity: 1;
  border-color: rgb(217 119 6 / var(--tw-border-opacity, 1));
}
.border-amber-700 {
  --tw-border-opacity: 1;
  border-color: rgb(180 83 9 / var(--tw-border-opacity, 1));
}
.border-amber-800 {
  --tw-border-opacity: 1;
  border-color: rgb(146 64 14 / var(--tw-border-opacity, 1));
}
.border-amber-900 {
  --tw-border-opacity: 1;
  border-color: rgb(120 53 15 / var(--tw-border-opacity, 1));
}
.border-amber-950 {
  --tw-border-opacity: 1;
  border-color: rgb(69 26 3 / var(--tw-border-opacity, 1));
}
.border-blue-100 {
  --tw-border-opacity: 1;
  border-color: rgb(219 234 254 / var(--tw-border-opacity, 1));
}
.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-blue-300 {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-blue-50 {
  --tw-border-opacity: 1;
  border-color: rgb(239 246 255 / var(--tw-border-opacity, 1));
}
.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.border-blue-700 {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}
.border-blue-800 {
  --tw-border-opacity: 1;
  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));
}
.border-blue-900 {
  --tw-border-opacity: 1;
  border-color: rgb(30 58 138 / var(--tw-border-opacity, 1));
}
.border-blue-950 {
  --tw-border-opacity: 1;
  border-color: rgb(23 37 84 / var(--tw-border-opacity, 1));
}
.border-cyan-100 {
  --tw-border-opacity: 1;
  border-color: rgb(207 250 254 / var(--tw-border-opacity, 1));
}
.border-cyan-200 {
  --tw-border-opacity: 1;
  border-color: rgb(165 243 252 / var(--tw-border-opacity, 1));
}
.border-cyan-300 {
  --tw-border-opacity: 1;
  border-color: rgb(103 232 249 / var(--tw-border-opacity, 1));
}
.border-cyan-400 {
  --tw-border-opacity: 1;
  border-color: rgb(34 211 238 / var(--tw-border-opacity, 1));
}
.border-cyan-50 {
  --tw-border-opacity: 1;
  border-color: rgb(236 254 255 / var(--tw-border-opacity, 1));
}
.border-cyan-500 {
  --tw-border-opacity: 1;
  border-color: rgb(6 182 212 / var(--tw-border-opacity, 1));
}
.border-cyan-600 {
  --tw-border-opacity: 1;
  border-color: rgb(8 145 178 / var(--tw-border-opacity, 1));
}
.border-cyan-700 {
  --tw-border-opacity: 1;
  border-color: rgb(14 116 144 / var(--tw-border-opacity, 1));
}
.border-cyan-800 {
  --tw-border-opacity: 1;
  border-color: rgb(21 94 117 / var(--tw-border-opacity, 1));
}
.border-cyan-900 {
  --tw-border-opacity: 1;
  border-color: rgb(22 78 99 / var(--tw-border-opacity, 1));
}
.border-cyan-950 {
  --tw-border-opacity: 1;
  border-color: rgb(8 51 68 / var(--tw-border-opacity, 1));
}
.border-emerald-100 {
  --tw-border-opacity: 1;
  border-color: rgb(209 250 229 / var(--tw-border-opacity, 1));
}
.border-emerald-200 {
  --tw-border-opacity: 1;
  border-color: rgb(167 243 208 / var(--tw-border-opacity, 1));
}
.border-emerald-300 {
  --tw-border-opacity: 1;
  border-color: rgb(110 231 183 / var(--tw-border-opacity, 1));
}
.border-emerald-400 {
  --tw-border-opacity: 1;
  border-color: rgb(52 211 153 / var(--tw-border-opacity, 1));
}
.border-emerald-50 {
  --tw-border-opacity: 1;
  border-color: rgb(236 253 245 / var(--tw-border-opacity, 1));
}
.border-emerald-500 {
  --tw-border-opacity: 1;
  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));
}
.border-emerald-600 {
  --tw-border-opacity: 1;
  border-color: rgb(5 150 105 / var(--tw-border-opacity, 1));
}
.border-emerald-700 {
  --tw-border-opacity: 1;
  border-color: rgb(4 120 87 / var(--tw-border-opacity, 1));
}
.border-emerald-800 {
  --tw-border-opacity: 1;
  border-color: rgb(6 95 70 / var(--tw-border-opacity, 1));
}
.border-emerald-900 {
  --tw-border-opacity: 1;
  border-color: rgb(6 78 59 / var(--tw-border-opacity, 1));
}
.border-emerald-950 {
  --tw-border-opacity: 1;
  border-color: rgb(2 44 34 / var(--tw-border-opacity, 1));
}
.border-fuchsia-100 {
  --tw-border-opacity: 1;
  border-color: rgb(250 232 255 / var(--tw-border-opacity, 1));
}
.border-fuchsia-200 {
  --tw-border-opacity: 1;
  border-color: rgb(245 208 254 / var(--tw-border-opacity, 1));
}
.border-fuchsia-300 {
  --tw-border-opacity: 1;
  border-color: rgb(240 171 252 / var(--tw-border-opacity, 1));
}
.border-fuchsia-400 {
  --tw-border-opacity: 1;
  border-color: rgb(232 121 249 / var(--tw-border-opacity, 1));
}
.border-fuchsia-50 {
  --tw-border-opacity: 1;
  border-color: rgb(253 244 255 / var(--tw-border-opacity, 1));
}
.border-fuchsia-500 {
  --tw-border-opacity: 1;
  border-color: rgb(217 70 239 / var(--tw-border-opacity, 1));
}
.border-fuchsia-600 {
  --tw-border-opacity: 1;
  border-color: rgb(192 38 211 / var(--tw-border-opacity, 1));
}
.border-fuchsia-700 {
  --tw-border-opacity: 1;
  border-color: rgb(162 28 175 / var(--tw-border-opacity, 1));
}
.border-fuchsia-800 {
  --tw-border-opacity: 1;
  border-color: rgb(134 25 143 / var(--tw-border-opacity, 1));
}
.border-fuchsia-900 {
  --tw-border-opacity: 1;
  border-color: rgb(112 26 117 / var(--tw-border-opacity, 1));
}
.border-fuchsia-950 {
  --tw-border-opacity: 1;
  border-color: rgb(74 4 78 / var(--tw-border-opacity, 1));
}
.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.border-gray-50 {
  --tw-border-opacity: 1;
  border-color: rgb(249 250 251 / var(--tw-border-opacity, 1));
}
.border-gray-500 {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}
.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}
.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.border-gray-800 {
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));
}
.border-gray-900 {
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}
.border-gray-950 {
  --tw-border-opacity: 1;
  border-color: rgb(3 7 18 / var(--tw-border-opacity, 1));
}
.border-green-100 {
  --tw-border-opacity: 1;
  border-color: rgb(220 252 231 / var(--tw-border-opacity, 1));
}
.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}
.border-green-300 {
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}
.border-green-400 {
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}
.border-green-50 {
  --tw-border-opacity: 1;
  border-color: rgb(240 253 244 / var(--tw-border-opacity, 1));
}
.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}
.border-green-600 {
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}
.border-green-700 {
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
}
.border-green-800 {
  --tw-border-opacity: 1;
  border-color: rgb(22 101 52 / var(--tw-border-opacity, 1));
}
.border-green-900 {
  --tw-border-opacity: 1;
  border-color: rgb(20 83 45 / var(--tw-border-opacity, 1));
}
.border-green-950 {
  --tw-border-opacity: 1;
  border-color: rgb(5 46 22 / var(--tw-border-opacity, 1));
}
.border-indigo-100 {
  --tw-border-opacity: 1;
  border-color: rgb(224 231 255 / var(--tw-border-opacity, 1));
}
.border-indigo-200 {
  --tw-border-opacity: 1;
  border-color: rgb(199 210 254 / var(--tw-border-opacity, 1));
}
.border-indigo-300 {
  --tw-border-opacity: 1;
  border-color: rgb(165 180 252 / var(--tw-border-opacity, 1));
}
.border-indigo-400 {
  --tw-border-opacity: 1;
  border-color: rgb(129 140 248 / var(--tw-border-opacity, 1));
}
.border-indigo-50 {
  --tw-border-opacity: 1;
  border-color: rgb(238 242 255 / var(--tw-border-opacity, 1));
}
.border-indigo-500 {
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}
.border-indigo-600 {
  --tw-border-opacity: 1;
  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));
}
.border-indigo-700 {
  --tw-border-opacity: 1;
  border-color: rgb(67 56 202 / var(--tw-border-opacity, 1));
}
.border-indigo-800 {
  --tw-border-opacity: 1;
  border-color: rgb(55 48 163 / var(--tw-border-opacity, 1));
}
.border-indigo-900 {
  --tw-border-opacity: 1;
  border-color: rgb(49 46 129 / var(--tw-border-opacity, 1));
}
.border-indigo-950 {
  --tw-border-opacity: 1;
  border-color: rgb(30 27 75 / var(--tw-border-opacity, 1));
}
.border-lime-100 {
  --tw-border-opacity: 1;
  border-color: rgb(236 252 203 / var(--tw-border-opacity, 1));
}
.border-lime-200 {
  --tw-border-opacity: 1;
  border-color: rgb(217 249 157 / var(--tw-border-opacity, 1));
}
.border-lime-300 {
  --tw-border-opacity: 1;
  border-color: rgb(190 242 100 / var(--tw-border-opacity, 1));
}
.border-lime-400 {
  --tw-border-opacity: 1;
  border-color: rgb(163 230 53 / var(--tw-border-opacity, 1));
}
.border-lime-50 {
  --tw-border-opacity: 1;
  border-color: rgb(247 254 231 / var(--tw-border-opacity, 1));
}
.border-lime-500 {
  --tw-border-opacity: 1;
  border-color: rgb(132 204 22 / var(--tw-border-opacity, 1));
}
.border-lime-600 {
  --tw-border-opacity: 1;
  border-color: rgb(101 163 13 / var(--tw-border-opacity, 1));
}
.border-lime-700 {
  --tw-border-opacity: 1;
  border-color: rgb(77 124 15 / var(--tw-border-opacity, 1));
}
.border-lime-800 {
  --tw-border-opacity: 1;
  border-color: rgb(63 98 18 / var(--tw-border-opacity, 1));
}
.border-lime-900 {
  --tw-border-opacity: 1;
  border-color: rgb(54 83 20 / var(--tw-border-opacity, 1));
}
.border-lime-950 {
  --tw-border-opacity: 1;
  border-color: rgb(26 46 5 / var(--tw-border-opacity, 1));
}
.border-neutral-100 {
  --tw-border-opacity: 1;
  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));
}
.border-neutral-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));
}
.border-neutral-300 {
  --tw-border-opacity: 1;
  border-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
}
.border-neutral-400 {
  --tw-border-opacity: 1;
  border-color: rgb(163 163 163 / var(--tw-border-opacity, 1));
}
.border-neutral-50 {
  --tw-border-opacity: 1;
  border-color: rgb(250 250 250 / var(--tw-border-opacity, 1));
}
.border-neutral-500 {
  --tw-border-opacity: 1;
  border-color: rgb(115 115 115 / var(--tw-border-opacity, 1));
}
.border-neutral-600 {
  --tw-border-opacity: 1;
  border-color: rgb(82 82 82 / var(--tw-border-opacity, 1));
}
.border-neutral-700 {
  --tw-border-opacity: 1;
  border-color: rgb(64 64 64 / var(--tw-border-opacity, 1));
}
.border-neutral-800 {
  --tw-border-opacity: 1;
  border-color: rgb(38 38 38 / var(--tw-border-opacity, 1));
}
.border-neutral-900 {
  --tw-border-opacity: 1;
  border-color: rgb(23 23 23 / var(--tw-border-opacity, 1));
}
.border-neutral-950 {
  --tw-border-opacity: 1;
  border-color: rgb(10 10 10 / var(--tw-border-opacity, 1));
}
.border-orange-100 {
  --tw-border-opacity: 1;
  border-color: rgb(255 237 213 / var(--tw-border-opacity, 1));
}
.border-orange-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));
}
.border-orange-300 {
  --tw-border-opacity: 1;
  border-color: rgb(253 186 116 / var(--tw-border-opacity, 1));
}
.border-orange-400 {
  --tw-border-opacity: 1;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}
.border-orange-50 {
  --tw-border-opacity: 1;
  border-color: rgb(255 247 237 / var(--tw-border-opacity, 1));
}
.border-orange-500 {
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.border-orange-600 {
  --tw-border-opacity: 1;
  border-color: rgb(234 88 12 / var(--tw-border-opacity, 1));
}
.border-orange-700 {
  --tw-border-opacity: 1;
  border-color: rgb(194 65 12 / var(--tw-border-opacity, 1));
}
.border-orange-800 {
  --tw-border-opacity: 1;
  border-color: rgb(154 52 18 / var(--tw-border-opacity, 1));
}
.border-orange-900 {
  --tw-border-opacity: 1;
  border-color: rgb(124 45 18 / var(--tw-border-opacity, 1));
}
.border-orange-950 {
  --tw-border-opacity: 1;
  border-color: rgb(67 20 7 / var(--tw-border-opacity, 1));
}
.border-pink-100 {
  --tw-border-opacity: 1;
  border-color: rgb(252 231 243 / var(--tw-border-opacity, 1));
}
.border-pink-200 {
  --tw-border-opacity: 1;
  border-color: rgb(251 207 232 / var(--tw-border-opacity, 1));
}
.border-pink-300 {
  --tw-border-opacity: 1;
  border-color: rgb(249 168 212 / var(--tw-border-opacity, 1));
}
.border-pink-400 {
  --tw-border-opacity: 1;
  border-color: rgb(244 114 182 / var(--tw-border-opacity, 1));
}
.border-pink-50 {
  --tw-border-opacity: 1;
  border-color: rgb(253 242 248 / var(--tw-border-opacity, 1));
}
.border-pink-500 {
  --tw-border-opacity: 1;
  border-color: rgb(236 72 153 / var(--tw-border-opacity, 1));
}
.border-pink-600 {
  --tw-border-opacity: 1;
  border-color: rgb(219 39 119 / var(--tw-border-opacity, 1));
}
.border-pink-700 {
  --tw-border-opacity: 1;
  border-color: rgb(190 24 93 / var(--tw-border-opacity, 1));
}
.border-pink-800 {
  --tw-border-opacity: 1;
  border-color: rgb(157 23 77 / var(--tw-border-opacity, 1));
}
.border-pink-900 {
  --tw-border-opacity: 1;
  border-color: rgb(131 24 67 / var(--tw-border-opacity, 1));
}
.border-pink-950 {
  --tw-border-opacity: 1;
  border-color: rgb(80 7 36 / var(--tw-border-opacity, 1));
}
.border-purple-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 232 255 / var(--tw-border-opacity, 1));
}
.border-purple-200 {
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}
.border-purple-300 {
  --tw-border-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));
}
.border-purple-400 {
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.border-purple-50 {
  --tw-border-opacity: 1;
  border-color: rgb(250 245 255 / var(--tw-border-opacity, 1));
}
.border-purple-500 {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.border-purple-600 {
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}
.border-purple-700 {
  --tw-border-opacity: 1;
  border-color: rgb(126 34 206 / var(--tw-border-opacity, 1));
}
.border-purple-800 {
  --tw-border-opacity: 1;
  border-color: rgb(107 33 168 / var(--tw-border-opacity, 1));
}
.border-purple-900 {
  --tw-border-opacity: 1;
  border-color: rgb(88 28 135 / var(--tw-border-opacity, 1));
}
.border-purple-950 {
  --tw-border-opacity: 1;
  border-color: rgb(59 7 100 / var(--tw-border-opacity, 1));
}
.border-red-100 {
  --tw-border-opacity: 1;
  border-color: rgb(254 226 226 / var(--tw-border-opacity, 1));
}
.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}
.border-red-50 {
  --tw-border-opacity: 1;
  border-color: rgb(254 242 242 / var(--tw-border-opacity, 1));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}
.border-red-700 {
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
}
.border-red-800 {
  --tw-border-opacity: 1;
  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));
}
.border-red-900 {
  --tw-border-opacity: 1;
  border-color: rgb(127 29 29 / var(--tw-border-opacity, 1));
}
.border-red-950 {
  --tw-border-opacity: 1;
  border-color: rgb(69 10 10 / var(--tw-border-opacity, 1));
}
.border-rose-100 {
  --tw-border-opacity: 1;
  border-color: rgb(255 228 230 / var(--tw-border-opacity, 1));
}
.border-rose-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 205 211 / var(--tw-border-opacity, 1));
}
.border-rose-300 {
  --tw-border-opacity: 1;
  border-color: rgb(253 164 175 / var(--tw-border-opacity, 1));
}
.border-rose-400 {
  --tw-border-opacity: 1;
  border-color: rgb(251 113 133 / var(--tw-border-opacity, 1));
}
.border-rose-50 {
  --tw-border-opacity: 1;
  border-color: rgb(255 241 242 / var(--tw-border-opacity, 1));
}
.border-rose-500 {
  --tw-border-opacity: 1;
  border-color: rgb(244 63 94 / var(--tw-border-opacity, 1));
}
.border-rose-600 {
  --tw-border-opacity: 1;
  border-color: rgb(225 29 72 / var(--tw-border-opacity, 1));
}
.border-rose-700 {
  --tw-border-opacity: 1;
  border-color: rgb(190 18 60 / var(--tw-border-opacity, 1));
}
.border-rose-800 {
  --tw-border-opacity: 1;
  border-color: rgb(159 18 57 / var(--tw-border-opacity, 1));
}
.border-rose-900 {
  --tw-border-opacity: 1;
  border-color: rgb(136 19 55 / var(--tw-border-opacity, 1));
}
.border-rose-950 {
  --tw-border-opacity: 1;
  border-color: rgb(76 5 25 / var(--tw-border-opacity, 1));
}
.border-sky-100 {
  --tw-border-opacity: 1;
  border-color: rgb(224 242 254 / var(--tw-border-opacity, 1));
}
.border-sky-200 {
  --tw-border-opacity: 1;
  border-color: rgb(186 230 253 / var(--tw-border-opacity, 1));
}
.border-sky-300 {
  --tw-border-opacity: 1;
  border-color: rgb(125 211 252 / var(--tw-border-opacity, 1));
}
.border-sky-400 {
  --tw-border-opacity: 1;
  border-color: rgb(56 189 248 / var(--tw-border-opacity, 1));
}
.border-sky-50 {
  --tw-border-opacity: 1;
  border-color: rgb(240 249 255 / var(--tw-border-opacity, 1));
}
.border-sky-500 {
  --tw-border-opacity: 1;
  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));
}
.border-sky-600 {
  --tw-border-opacity: 1;
  border-color: rgb(2 132 199 / var(--tw-border-opacity, 1));
}
.border-sky-700 {
  --tw-border-opacity: 1;
  border-color: rgb(3 105 161 / var(--tw-border-opacity, 1));
}
.border-sky-800 {
  --tw-border-opacity: 1;
  border-color: rgb(7 89 133 / var(--tw-border-opacity, 1));
}
.border-sky-900 {
  --tw-border-opacity: 1;
  border-color: rgb(12 74 110 / var(--tw-border-opacity, 1));
}
.border-sky-950 {
  --tw-border-opacity: 1;
  border-color: rgb(8 47 73 / var(--tw-border-opacity, 1));
}
.border-slate-100 {
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity, 1));
}
.border-slate-200 {
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
}
.border-slate-300 {
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}
.border-slate-400 {
  --tw-border-opacity: 1;
  border-color: rgb(148 163 184 / var(--tw-border-opacity, 1));
}
.border-slate-50 {
  --tw-border-opacity: 1;
  border-color: rgb(248 250 252 / var(--tw-border-opacity, 1));
}
.border-slate-500 {
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.border-slate-600 {
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));
}
.border-slate-700 {
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));
}
.border-slate-800 {
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));
}
.border-slate-900 {
  --tw-border-opacity: 1;
  border-color: rgb(15 23 42 / var(--tw-border-opacity, 1));
}
.border-slate-950 {
  --tw-border-opacity: 1;
  border-color: rgb(2 6 23 / var(--tw-border-opacity, 1));
}
.border-stone-100 {
  --tw-border-opacity: 1;
  border-color: rgb(245 245 244 / var(--tw-border-opacity, 1));
}
.border-stone-200 {
  --tw-border-opacity: 1;
  border-color: rgb(231 229 228 / var(--tw-border-opacity, 1));
}
.border-stone-300 {
  --tw-border-opacity: 1;
  border-color: rgb(214 211 209 / var(--tw-border-opacity, 1));
}
.border-stone-400 {
  --tw-border-opacity: 1;
  border-color: rgb(168 162 158 / var(--tw-border-opacity, 1));
}
.border-stone-50 {
  --tw-border-opacity: 1;
  border-color: rgb(250 250 249 / var(--tw-border-opacity, 1));
}
.border-stone-500 {
  --tw-border-opacity: 1;
  border-color: rgb(120 113 108 / var(--tw-border-opacity, 1));
}
.border-stone-600 {
  --tw-border-opacity: 1;
  border-color: rgb(87 83 78 / var(--tw-border-opacity, 1));
}
.border-stone-700 {
  --tw-border-opacity: 1;
  border-color: rgb(68 64 60 / var(--tw-border-opacity, 1));
}
.border-stone-800 {
  --tw-border-opacity: 1;
  border-color: rgb(41 37 36 / var(--tw-border-opacity, 1));
}
.border-stone-900 {
  --tw-border-opacity: 1;
  border-color: rgb(28 25 23 / var(--tw-border-opacity, 1));
}
.border-stone-950 {
  --tw-border-opacity: 1;
  border-color: rgb(12 10 9 / var(--tw-border-opacity, 1));
}
.border-teal-100 {
  --tw-border-opacity: 1;
  border-color: rgb(204 251 241 / var(--tw-border-opacity, 1));
}
.border-teal-200 {
  --tw-border-opacity: 1;
  border-color: rgb(153 246 228 / var(--tw-border-opacity, 1));
}
.border-teal-300 {
  --tw-border-opacity: 1;
  border-color: rgb(94 234 212 / var(--tw-border-opacity, 1));
}
.border-teal-400 {
  --tw-border-opacity: 1;
  border-color: rgb(45 212 191 / var(--tw-border-opacity, 1));
}
.border-teal-50 {
  --tw-border-opacity: 1;
  border-color: rgb(240 253 250 / var(--tw-border-opacity, 1));
}
.border-teal-500 {
  --tw-border-opacity: 1;
  border-color: rgb(20 184 166 / var(--tw-border-opacity, 1));
}
.border-teal-600 {
  --tw-border-opacity: 1;
  border-color: rgb(13 148 136 / var(--tw-border-opacity, 1));
}
.border-teal-700 {
  --tw-border-opacity: 1;
  border-color: rgb(15 118 110 / var(--tw-border-opacity, 1));
}
.border-teal-800 {
  --tw-border-opacity: 1;
  border-color: rgb(17 94 89 / var(--tw-border-opacity, 1));
}
.border-teal-900 {
  --tw-border-opacity: 1;
  border-color: rgb(19 78 74 / var(--tw-border-opacity, 1));
}
.border-teal-950 {
  --tw-border-opacity: 1;
  border-color: rgb(4 47 46 / var(--tw-border-opacity, 1));
}
.border-transparent {
  border-color: transparent;
}
.border-tremor-background {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-tremor-border {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-tremor-brand {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-tremor-brand-emphasis {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}
.border-tremor-brand-inverted {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-tremor-brand-subtle {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-violet-100 {
  --tw-border-opacity: 1;
  border-color: rgb(237 233 254 / var(--tw-border-opacity, 1));
}
.border-violet-200 {
  --tw-border-opacity: 1;
  border-color: rgb(221 214 254 / var(--tw-border-opacity, 1));
}
.border-violet-300 {
  --tw-border-opacity: 1;
  border-color: rgb(196 181 253 / var(--tw-border-opacity, 1));
}
.border-violet-400 {
  --tw-border-opacity: 1;
  border-color: rgb(167 139 250 / var(--tw-border-opacity, 1));
}
.border-violet-50 {
  --tw-border-opacity: 1;
  border-color: rgb(245 243 255 / var(--tw-border-opacity, 1));
}
.border-violet-500 {
  --tw-border-opacity: 1;
  border-color: rgb(139 92 246 / var(--tw-border-opacity, 1));
}
.border-violet-600 {
  --tw-border-opacity: 1;
  border-color: rgb(124 58 237 / var(--tw-border-opacity, 1));
}
.border-violet-700 {
  --tw-border-opacity: 1;
  border-color: rgb(109 40 217 / var(--tw-border-opacity, 1));
}
.border-violet-800 {
  --tw-border-opacity: 1;
  border-color: rgb(91 33 182 / var(--tw-border-opacity, 1));
}
.border-violet-900 {
  --tw-border-opacity: 1;
  border-color: rgb(76 29 149 / var(--tw-border-opacity, 1));
}
.border-violet-950 {
  --tw-border-opacity: 1;
  border-color: rgb(46 16 101 / var(--tw-border-opacity, 1));
}
.border-yellow-100 {
  --tw-border-opacity: 1;
  border-color: rgb(254 249 195 / var(--tw-border-opacity, 1));
}
.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}
.border-yellow-300 {
  --tw-border-opacity: 1;
  border-color: rgb(253 224 71 / var(--tw-border-opacity, 1));
}
.border-yellow-400 {
  --tw-border-opacity: 1;
  border-color: rgb(250 204 21 / var(--tw-border-opacity, 1));
}
.border-yellow-50 {
  --tw-border-opacity: 1;
  border-color: rgb(254 252 232 / var(--tw-border-opacity, 1));
}
.border-yellow-500 {
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));
}
.border-yellow-600 {
  --tw-border-opacity: 1;
  border-color: rgb(202 138 4 / var(--tw-border-opacity, 1));
}
.border-yellow-700 {
  --tw-border-opacity: 1;
  border-color: rgb(161 98 7 / var(--tw-border-opacity, 1));
}
.border-yellow-800 {
  --tw-border-opacity: 1;
  border-color: rgb(133 77 14 / var(--tw-border-opacity, 1));
}
.border-yellow-900 {
  --tw-border-opacity: 1;
  border-color: rgb(113 63 18 / var(--tw-border-opacity, 1));
}
.border-yellow-950 {
  --tw-border-opacity: 1;
  border-color: rgb(66 32 6 / var(--tw-border-opacity, 1));
}
.border-zinc-100 {
  --tw-border-opacity: 1;
  border-color: rgb(244 244 245 / var(--tw-border-opacity, 1));
}
.border-zinc-200 {
  --tw-border-opacity: 1;
  border-color: rgb(228 228 231 / var(--tw-border-opacity, 1));
}
.border-zinc-300 {
  --tw-border-opacity: 1;
  border-color: rgb(212 212 216 / var(--tw-border-opacity, 1));
}
.border-zinc-400 {
  --tw-border-opacity: 1;
  border-color: rgb(161 161 170 / var(--tw-border-opacity, 1));
}
.border-zinc-50 {
  --tw-border-opacity: 1;
  border-color: rgb(250 250 250 / var(--tw-border-opacity, 1));
}
.border-zinc-500 {
  --tw-border-opacity: 1;
  border-color: rgb(113 113 122 / var(--tw-border-opacity, 1));
}
.border-zinc-600 {
  --tw-border-opacity: 1;
  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));
}
.border-zinc-700 {
  --tw-border-opacity: 1;
  border-color: rgb(63 63 70 / var(--tw-border-opacity, 1));
}
.border-zinc-800 {
  --tw-border-opacity: 1;
  border-color: rgb(39 39 42 / var(--tw-border-opacity, 1));
}
.border-zinc-900 {
  --tw-border-opacity: 1;
  border-color: rgb(24 24 27 / var(--tw-border-opacity, 1));
}
.border-zinc-950 {
  --tw-border-opacity: 1;
  border-color: rgb(9 9 11 / var(--tw-border-opacity, 1));
}
.bg-amber-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));
}
.bg-amber-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 230 138 / var(--tw-bg-opacity, 1));
}
.bg-amber-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 211 77 / var(--tw-bg-opacity, 1));
}
.bg-amber-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 191 36 / var(--tw-bg-opacity, 1));
}
.bg-amber-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}
.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}
.bg-amber-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1));
}
.bg-amber-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(180 83 9 / var(--tw-bg-opacity, 1));
}
.bg-amber-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(146 64 14 / var(--tw-bg-opacity, 1));
}
.bg-amber-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(120 53 15 / var(--tw-bg-opacity, 1));
}
.bg-amber-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(69 26 3 / var(--tw-bg-opacity, 1));
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));
}
.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.bg-blue-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}
.bg-blue-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}
.bg-blue-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(23 37 84 / var(--tw-bg-opacity, 1));
}
.bg-cyan-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));
}
.bg-cyan-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(165 243 252 / var(--tw-bg-opacity, 1));
}
.bg-cyan-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(103 232 249 / var(--tw-bg-opacity, 1));
}
.bg-cyan-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 211 238 / var(--tw-bg-opacity, 1));
}
.bg-cyan-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));
}
.bg-cyan-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));
}
.bg-cyan-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(8 145 178 / var(--tw-bg-opacity, 1));
}
.bg-cyan-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 116 144 / var(--tw-bg-opacity, 1));
}
.bg-cyan-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(21 94 117 / var(--tw-bg-opacity, 1));
}
.bg-cyan-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 78 99 / var(--tw-bg-opacity, 1));
}
.bg-cyan-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(8 51 68 / var(--tw-bg-opacity, 1));
}
.bg-emerald-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));
}
.bg-emerald-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(167 243 208 / var(--tw-bg-opacity, 1));
}
.bg-emerald-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(110 231 183 / var(--tw-bg-opacity, 1));
}
.bg-emerald-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(52 211 153 / var(--tw-bg-opacity, 1));
}
.bg-emerald-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));
}
.bg-emerald-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}
.bg-emerald-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(5 150 105 / var(--tw-bg-opacity, 1));
}
.bg-emerald-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(4 120 87 / var(--tw-bg-opacity, 1));
}
.bg-emerald-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(6 95 70 / var(--tw-bg-opacity, 1));
}
.bg-emerald-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(6 78 59 / var(--tw-bg-opacity, 1));
}
.bg-emerald-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 44 34 / var(--tw-bg-opacity, 1));
}
.bg-fuchsia-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 232 255 / var(--tw-bg-opacity, 1));
}
.bg-fuchsia-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 208 254 / var(--tw-bg-opacity, 1));
}
.bg-fuchsia-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 171 252 / var(--tw-bg-opacity, 1));
}
.bg-fuchsia-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(232 121 249 / var(--tw-bg-opacity, 1));
}
.bg-fuchsia-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 244 255 / var(--tw-bg-opacity, 1));
}
.bg-fuchsia-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 70 239 / var(--tw-bg-opacity, 1));
}
.bg-fuchsia-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(192 38 211 / var(--tw-bg-opacity, 1));
}
.bg-fuchsia-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(162 28 175 / var(--tw-bg-opacity, 1));
}
.bg-fuchsia-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(134 25 143 / var(--tw-bg-opacity, 1));
}
.bg-fuchsia-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(112 26 117 / var(--tw-bg-opacity, 1));
}
.bg-fuchsia-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(74 4 78 / var(--tw-bg-opacity, 1));
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.bg-gray-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(3 7 18 / var(--tw-bg-opacity, 1));
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));
}
.bg-green-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(134 239 172 / var(--tw-bg-opacity, 1));
}
.bg-green-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}
.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.bg-green-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}
.bg-green-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 101 52 / var(--tw-bg-opacity, 1));
}
.bg-green-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
}
.bg-green-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(5 46 22 / var(--tw-bg-opacity, 1));
}
.bg-indigo-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}
.bg-indigo-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(199 210 254 / var(--tw-bg-opacity, 1));
}
.bg-indigo-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(165 180 252 / var(--tw-bg-opacity, 1));
}
.bg-indigo-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(129 140 248 / var(--tw-bg-opacity, 1));
}
.bg-indigo-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}
.bg-indigo-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}
.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}
.bg-indigo-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));
}
.bg-indigo-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 48 163 / var(--tw-bg-opacity, 1));
}
.bg-indigo-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(49 46 129 / var(--tw-bg-opacity, 1));
}
.bg-indigo-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 27 75 / var(--tw-bg-opacity, 1));
}
.bg-lime-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 252 203 / var(--tw-bg-opacity, 1));
}
.bg-lime-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 249 157 / var(--tw-bg-opacity, 1));
}
.bg-lime-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(190 242 100 / var(--tw-bg-opacity, 1));
}
.bg-lime-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(163 230 53 / var(--tw-bg-opacity, 1));
}
.bg-lime-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(247 254 231 / var(--tw-bg-opacity, 1));
}
.bg-lime-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(132 204 22 / var(--tw-bg-opacity, 1));
}
.bg-lime-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(101 163 13 / var(--tw-bg-opacity, 1));
}
.bg-lime-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(77 124 15 / var(--tw-bg-opacity, 1));
}
.bg-lime-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(63 98 18 / var(--tw-bg-opacity, 1));
}
.bg-lime-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(54 83 20 / var(--tw-bg-opacity, 1));
}
.bg-lime-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(26 46 5 / var(--tw-bg-opacity, 1));
}
.bg-neutral-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}
.bg-neutral-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity, 1));
}
.bg-neutral-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 212 / var(--tw-bg-opacity, 1));
}
.bg-neutral-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(163 163 163 / var(--tw-bg-opacity, 1));
}
.bg-neutral-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}
.bg-neutral-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(115 115 115 / var(--tw-bg-opacity, 1));
}
.bg-neutral-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 82 / var(--tw-bg-opacity, 1));
}
.bg-neutral-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(64 64 64 / var(--tw-bg-opacity, 1));
}
.bg-neutral-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(38 38 38 / var(--tw-bg-opacity, 1));
}
.bg-neutral-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(23 23 23 / var(--tw-bg-opacity, 1));
}
.bg-neutral-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(10 10 10 / var(--tw-bg-opacity, 1));
}
.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}
.bg-orange-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 215 170 / var(--tw-bg-opacity, 1));
}
.bg-orange-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 186 116 / var(--tw-bg-opacity, 1));
}
.bg-orange-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 146 60 / var(--tw-bg-opacity, 1));
}
.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}
.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-orange-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}
.bg-orange-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(194 65 12 / var(--tw-bg-opacity, 1));
}
.bg-orange-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(154 52 18 / var(--tw-bg-opacity, 1));
}
.bg-orange-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(124 45 18 / var(--tw-bg-opacity, 1));
}
.bg-orange-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(67 20 7 / var(--tw-bg-opacity, 1));
}
.bg-pink-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 231 243 / var(--tw-bg-opacity, 1));
}
.bg-pink-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 207 232 / var(--tw-bg-opacity, 1));
}
.bg-pink-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 168 212 / var(--tw-bg-opacity, 1));
}
.bg-pink-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 114 182 / var(--tw-bg-opacity, 1));
}
.bg-pink-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 242 248 / var(--tw-bg-opacity, 1));
}
.bg-pink-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));
}
.bg-pink-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 39 119 / var(--tw-bg-opacity, 1));
}
.bg-pink-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(190 24 93 / var(--tw-bg-opacity, 1));
}
.bg-pink-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(157 23 77 / var(--tw-bg-opacity, 1));
}
.bg-pink-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(131 24 67 / var(--tw-bg-opacity, 1));
}
.bg-pink-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(80 7 36 / var(--tw-bg-opacity, 1));
}
.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(216 180 254 / var(--tw-bg-opacity, 1));
}
.bg-purple-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));
}
.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.bg-purple-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}
.bg-purple-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 33 168 / var(--tw-bg-opacity, 1));
}
.bg-purple-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));
}
.bg-purple-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 7 100 / var(--tw-bg-opacity, 1));
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}
.bg-red-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 165 165 / var(--tw-bg-opacity, 1));
}
.bg-red-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-red-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}
.bg-red-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity, 1));
}
.bg-red-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));
}
.bg-red-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(69 10 10 / var(--tw-bg-opacity, 1));
}
.bg-rose-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 228 230 / var(--tw-bg-opacity, 1));
}
.bg-rose-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 205 211 / var(--tw-bg-opacity, 1));
}
.bg-rose-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 164 175 / var(--tw-bg-opacity, 1));
}
.bg-rose-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 113 133 / var(--tw-bg-opacity, 1));
}
.bg-rose-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 241 242 / var(--tw-bg-opacity, 1));
}
.bg-rose-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 63 94 / var(--tw-bg-opacity, 1));
}
.bg-rose-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(225 29 72 / var(--tw-bg-opacity, 1));
}
.bg-rose-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(190 18 60 / var(--tw-bg-opacity, 1));
}
.bg-rose-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(159 18 57 / var(--tw-bg-opacity, 1));
}
.bg-rose-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(136 19 55 / var(--tw-bg-opacity, 1));
}
.bg-rose-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(76 5 25 / var(--tw-bg-opacity, 1));
}
.bg-sky-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));
}
.bg-sky-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(186 230 253 / var(--tw-bg-opacity, 1));
}
.bg-sky-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(125 211 252 / var(--tw-bg-opacity, 1));
}
.bg-sky-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(56 189 248 / var(--tw-bg-opacity, 1));
}
.bg-sky-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));
}
.bg-sky-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity, 1));
}
.bg-sky-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));
}
.bg-sky-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(3 105 161 / var(--tw-bg-opacity, 1));
}
.bg-sky-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(7 89 133 / var(--tw-bg-opacity, 1));
}
.bg-sky-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(12 74 110 / var(--tw-bg-opacity, 1));
}
.bg-sky-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(8 47 73 / var(--tw-bg-opacity, 1));
}
.bg-slate-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}
.bg-slate-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));
}
.bg-slate-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(203 213 225 / var(--tw-bg-opacity, 1));
}
.bg-slate-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(148 163 184 / var(--tw-bg-opacity, 1));
}
.bg-slate-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}
.bg-slate-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));
}
.bg-slate-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}
.bg-slate-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));
}
.bg-slate-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
}
.bg-slate-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
}
.bg-slate-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 6 23 / var(--tw-bg-opacity, 1));
}
.bg-slate-950\/30 {
  background-color: rgb(2 6 23 / 0.3);
}
.bg-stone-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 244 / var(--tw-bg-opacity, 1));
}
.bg-stone-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(231 229 228 / var(--tw-bg-opacity, 1));
}
.bg-stone-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(214 211 209 / var(--tw-bg-opacity, 1));
}
.bg-stone-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 162 158 / var(--tw-bg-opacity, 1));
}
.bg-stone-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 249 / var(--tw-bg-opacity, 1));
}
.bg-stone-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(120 113 108 / var(--tw-bg-opacity, 1));
}
.bg-stone-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(87 83 78 / var(--tw-bg-opacity, 1));
}
.bg-stone-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(68 64 60 / var(--tw-bg-opacity, 1));
}
.bg-stone-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(41 37 36 / var(--tw-bg-opacity, 1));
}
.bg-stone-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(28 25 23 / var(--tw-bg-opacity, 1));
}
.bg-stone-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(12 10 9 / var(--tw-bg-opacity, 1));
}
.bg-teal-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));
}
.bg-teal-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(153 246 228 / var(--tw-bg-opacity, 1));
}
.bg-teal-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(94 234 212 / var(--tw-bg-opacity, 1));
}
.bg-teal-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(45 212 191 / var(--tw-bg-opacity, 1));
}
.bg-teal-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 250 / var(--tw-bg-opacity, 1));
}
.bg-teal-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity, 1));
}
.bg-teal-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(13 148 136 / var(--tw-bg-opacity, 1));
}
.bg-teal-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(15 118 110 / var(--tw-bg-opacity, 1));
}
.bg-teal-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 94 89 / var(--tw-bg-opacity, 1));
}
.bg-teal-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(19 78 74 / var(--tw-bg-opacity, 1));
}
.bg-teal-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(4 47 46 / var(--tw-bg-opacity, 1));
}
.bg-transparent {
  background-color: transparent;
}
.bg-tremor-background {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-tremor-background-emphasis {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.bg-tremor-background-muted {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-tremor-background-subtle {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-tremor-border {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-tremor-brand {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-tremor-brand-faint {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-tremor-brand-muted {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.bg-tremor-brand-muted\/50 {
  background-color: rgb(191 219 254 / 0.5);
}
.bg-tremor-brand-subtle {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}
.bg-tremor-content-subtle {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.bg-violet-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(237 233 254 / var(--tw-bg-opacity, 1));
}
.bg-violet-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(221 214 254 / var(--tw-bg-opacity, 1));
}
.bg-violet-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(196 181 253 / var(--tw-bg-opacity, 1));
}
.bg-violet-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(167 139 250 / var(--tw-bg-opacity, 1));
}
.bg-violet-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 243 255 / var(--tw-bg-opacity, 1));
}
.bg-violet-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(139 92 246 / var(--tw-bg-opacity, 1));
}
.bg-violet-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(124 58 237 / var(--tw-bg-opacity, 1));
}
.bg-violet-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(109 40 217 / var(--tw-bg-opacity, 1));
}
.bg-violet-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(91 33 182 / var(--tw-bg-opacity, 1));
}
.bg-violet-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(76 29 149 / var(--tw-bg-opacity, 1));
}
.bg-violet-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(46 16 101 / var(--tw-bg-opacity, 1));
}
.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-yellow-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));
}
.bg-yellow-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 224 71 / var(--tw-bg-opacity, 1));
}
.bg-yellow-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));
}
.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-yellow-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}
.bg-yellow-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));
}
.bg-yellow-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(133 77 14 / var(--tw-bg-opacity, 1));
}
.bg-yellow-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(113 63 18 / var(--tw-bg-opacity, 1));
}
.bg-yellow-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(66 32 6 / var(--tw-bg-opacity, 1));
}
.bg-zinc-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 245 / var(--tw-bg-opacity, 1));
}
.bg-zinc-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(228 228 231 / var(--tw-bg-opacity, 1));
}
.bg-zinc-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 216 / var(--tw-bg-opacity, 1));
}
.bg-zinc-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(161 161 170 / var(--tw-bg-opacity, 1));
}
.bg-zinc-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}
.bg-zinc-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(113 113 122 / var(--tw-bg-opacity, 1));
}
.bg-zinc-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));
}
.bg-zinc-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
}
.bg-zinc-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
}
.bg-zinc-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(24 24 27 / var(--tw-bg-opacity, 1));
}
.bg-zinc-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(9 9 11 / var(--tw-bg-opacity, 1));
}
.bg-opacity-10 {
  --tw-bg-opacity: 0.1;
}
.bg-opacity-20 {
  --tw-bg-opacity: 0.2;
}
.bg-opacity-40 {
  --tw-bg-opacity: 0.4;
}
.fill-amber-100 {
  fill: #fef3c7;
}
.fill-amber-200 {
  fill: #fde68a;
}
.fill-amber-300 {
  fill: #fcd34d;
}
.fill-amber-400 {
  fill: #fbbf24;
}
.fill-amber-50 {
  fill: #fffbeb;
}
.fill-amber-500 {
  fill: #f59e0b;
}
.fill-amber-600 {
  fill: #d97706;
}
.fill-amber-700 {
  fill: #b45309;
}
.fill-amber-800 {
  fill: #92400e;
}
.fill-amber-900 {
  fill: #78350f;
}
.fill-amber-950 {
  fill: #451a03;
}
.fill-blue-100 {
  fill: #dbeafe;
}
.fill-blue-200 {
  fill: #bfdbfe;
}
.fill-blue-300 {
  fill: #93c5fd;
}
.fill-blue-400 {
  fill: #60a5fa;
}
.fill-blue-50 {
  fill: #eff6ff;
}
.fill-blue-500 {
  fill: #3b82f6;
}
.fill-blue-600 {
  fill: #2563eb;
}
.fill-blue-700 {
  fill: #1d4ed8;
}
.fill-blue-800 {
  fill: #1e40af;
}
.fill-blue-900 {
  fill: #1e3a8a;
}
.fill-blue-950 {
  fill: #172554;
}
.fill-cyan-100 {
  fill: #cffafe;
}
.fill-cyan-200 {
  fill: #a5f3fc;
}
.fill-cyan-300 {
  fill: #67e8f9;
}
.fill-cyan-400 {
  fill: #22d3ee;
}
.fill-cyan-50 {
  fill: #ecfeff;
}
.fill-cyan-500 {
  fill: #06b6d4;
}
.fill-cyan-600 {
  fill: #0891b2;
}
.fill-cyan-700 {
  fill: #0e7490;
}
.fill-cyan-800 {
  fill: #155e75;
}
.fill-cyan-900 {
  fill: #164e63;
}
.fill-cyan-950 {
  fill: #083344;
}
.fill-emerald-100 {
  fill: #d1fae5;
}
.fill-emerald-200 {
  fill: #a7f3d0;
}
.fill-emerald-300 {
  fill: #6ee7b7;
}
.fill-emerald-400 {
  fill: #34d399;
}
.fill-emerald-50 {
  fill: #ecfdf5;
}
.fill-emerald-500 {
  fill: #10b981;
}
.fill-emerald-600 {
  fill: #059669;
}
.fill-emerald-700 {
  fill: #047857;
}
.fill-emerald-800 {
  fill: #065f46;
}
.fill-emerald-900 {
  fill: #064e3b;
}
.fill-emerald-950 {
  fill: #022c22;
}
.fill-fuchsia-100 {
  fill: #fae8ff;
}
.fill-fuchsia-200 {
  fill: #f5d0fe;
}
.fill-fuchsia-300 {
  fill: #f0abfc;
}
.fill-fuchsia-400 {
  fill: #e879f9;
}
.fill-fuchsia-50 {
  fill: #fdf4ff;
}
.fill-fuchsia-500 {
  fill: #d946ef;
}
.fill-fuchsia-600 {
  fill: #c026d3;
}
.fill-fuchsia-700 {
  fill: #a21caf;
}
.fill-fuchsia-800 {
  fill: #86198f;
}
.fill-fuchsia-900 {
  fill: #701a75;
}
.fill-fuchsia-950 {
  fill: #4a044e;
}
.fill-gray-100 {
  fill: #f3f4f6;
}
.fill-gray-200 {
  fill: #e5e7eb;
}
.fill-gray-300 {
  fill: #d1d5db;
}
.fill-gray-400 {
  fill: #9ca3af;
}
.fill-gray-50 {
  fill: #f9fafb;
}
.fill-gray-500 {
  fill: #6b7280;
}
.fill-gray-600 {
  fill: #4b5563;
}
.fill-gray-700 {
  fill: #374151;
}
.fill-gray-800 {
  fill: #1f2937;
}
.fill-gray-900 {
  fill: #111827;
}
.fill-gray-950 {
  fill: #030712;
}
.fill-green-100 {
  fill: #dcfce7;
}
.fill-green-200 {
  fill: #bbf7d0;
}
.fill-green-300 {
  fill: #86efac;
}
.fill-green-400 {
  fill: #4ade80;
}
.fill-green-50 {
  fill: #f0fdf4;
}
.fill-green-500 {
  fill: #22c55e;
}
.fill-green-600 {
  fill: #16a34a;
}
.fill-green-700 {
  fill: #15803d;
}
.fill-green-800 {
  fill: #166534;
}
.fill-green-900 {
  fill: #14532d;
}
.fill-green-950 {
  fill: #052e16;
}
.fill-indigo-100 {
  fill: #e0e7ff;
}
.fill-indigo-200 {
  fill: #c7d2fe;
}
.fill-indigo-300 {
  fill: #a5b4fc;
}
.fill-indigo-400 {
  fill: #818cf8;
}
.fill-indigo-50 {
  fill: #eef2ff;
}
.fill-indigo-500 {
  fill: #6366f1;
}
.fill-indigo-600 {
  fill: #4f46e5;
}
.fill-indigo-700 {
  fill: #4338ca;
}
.fill-indigo-800 {
  fill: #3730a3;
}
.fill-indigo-900 {
  fill: #312e81;
}
.fill-indigo-950 {
  fill: #1e1b4b;
}
.fill-lime-100 {
  fill: #ecfccb;
}
.fill-lime-200 {
  fill: #d9f99d;
}
.fill-lime-300 {
  fill: #bef264;
}
.fill-lime-400 {
  fill: #a3e635;
}
.fill-lime-50 {
  fill: #f7fee7;
}
.fill-lime-500 {
  fill: #84cc16;
}
.fill-lime-600 {
  fill: #65a30d;
}
.fill-lime-700 {
  fill: #4d7c0f;
}
.fill-lime-800 {
  fill: #3f6212;
}
.fill-lime-900 {
  fill: #365314;
}
.fill-lime-950 {
  fill: #1a2e05;
}
.fill-neutral-100 {
  fill: #f5f5f5;
}
.fill-neutral-200 {
  fill: #e5e5e5;
}
.fill-neutral-300 {
  fill: #d4d4d4;
}
.fill-neutral-400 {
  fill: #a3a3a3;
}
.fill-neutral-50 {
  fill: #fafafa;
}
.fill-neutral-500 {
  fill: #737373;
}
.fill-neutral-600 {
  fill: #525252;
}
.fill-neutral-700 {
  fill: #404040;
}
.fill-neutral-800 {
  fill: #262626;
}
.fill-neutral-900 {
  fill: #171717;
}
.fill-neutral-950 {
  fill: #0a0a0a;
}
.fill-orange-100 {
  fill: #ffedd5;
}
.fill-orange-200 {
  fill: #fed7aa;
}
.fill-orange-300 {
  fill: #fdba74;
}
.fill-orange-400 {
  fill: #fb923c;
}
.fill-orange-50 {
  fill: #fff7ed;
}
.fill-orange-500 {
  fill: #f97316;
}
.fill-orange-600 {
  fill: #ea580c;
}
.fill-orange-700 {
  fill: #c2410c;
}
.fill-orange-800 {
  fill: #9a3412;
}
.fill-orange-900 {
  fill: #7c2d12;
}
.fill-orange-950 {
  fill: #431407;
}
.fill-pink-100 {
  fill: #fce7f3;
}
.fill-pink-200 {
  fill: #fbcfe8;
}
.fill-pink-300 {
  fill: #f9a8d4;
}
.fill-pink-400 {
  fill: #f472b6;
}
.fill-pink-50 {
  fill: #fdf2f8;
}
.fill-pink-500 {
  fill: #ec4899;
}
.fill-pink-600 {
  fill: #db2777;
}
.fill-pink-700 {
  fill: #be185d;
}
.fill-pink-800 {
  fill: #9d174d;
}
.fill-pink-900 {
  fill: #831843;
}
.fill-pink-950 {
  fill: #500724;
}
.fill-purple-100 {
  fill: #f3e8ff;
}
.fill-purple-200 {
  fill: #e9d5ff;
}
.fill-purple-300 {
  fill: #d8b4fe;
}
.fill-purple-400 {
  fill: #c084fc;
}
.fill-purple-50 {
  fill: #faf5ff;
}
.fill-purple-500 {
  fill: #a855f7;
}
.fill-purple-600 {
  fill: #9333ea;
}
.fill-purple-700 {
  fill: #7e22ce;
}
.fill-purple-800 {
  fill: #6b21a8;
}
.fill-purple-900 {
  fill: #581c87;
}
.fill-purple-950 {
  fill: #3b0764;
}
.fill-red-100 {
  fill: #fee2e2;
}
.fill-red-200 {
  fill: #fecaca;
}
.fill-red-300 {
  fill: #fca5a5;
}
.fill-red-400 {
  fill: #f87171;
}
.fill-red-50 {
  fill: #fef2f2;
}
.fill-red-500 {
  fill: #ef4444;
}
.fill-red-600 {
  fill: #dc2626;
}
.fill-red-700 {
  fill: #b91c1c;
}
.fill-red-800 {
  fill: #991b1b;
}
.fill-red-900 {
  fill: #7f1d1d;
}
.fill-red-950 {
  fill: #450a0a;
}
.fill-rose-100 {
  fill: #ffe4e6;
}
.fill-rose-200 {
  fill: #fecdd3;
}
.fill-rose-300 {
  fill: #fda4af;
}
.fill-rose-400 {
  fill: #fb7185;
}
.fill-rose-50 {
  fill: #fff1f2;
}
.fill-rose-500 {
  fill: #f43f5e;
}
.fill-rose-600 {
  fill: #e11d48;
}
.fill-rose-700 {
  fill: #be123c;
}
.fill-rose-800 {
  fill: #9f1239;
}
.fill-rose-900 {
  fill: #881337;
}
.fill-rose-950 {
  fill: #4c0519;
}
.fill-sky-100 {
  fill: #e0f2fe;
}
.fill-sky-200 {
  fill: #bae6fd;
}
.fill-sky-300 {
  fill: #7dd3fc;
}
.fill-sky-400 {
  fill: #38bdf8;
}
.fill-sky-50 {
  fill: #f0f9ff;
}
.fill-sky-500 {
  fill: #0ea5e9;
}
.fill-sky-600 {
  fill: #0284c7;
}
.fill-sky-700 {
  fill: #0369a1;
}
.fill-sky-800 {
  fill: #075985;
}
.fill-sky-900 {
  fill: #0c4a6e;
}
.fill-sky-950 {
  fill: #082f49;
}
.fill-slate-100 {
  fill: #f1f5f9;
}
.fill-slate-200 {
  fill: #e2e8f0;
}
.fill-slate-300 {
  fill: #cbd5e1;
}
.fill-slate-400 {
  fill: #94a3b8;
}
.fill-slate-50 {
  fill: #f8fafc;
}
.fill-slate-500 {
  fill: #64748b;
}
.fill-slate-600 {
  fill: #475569;
}
.fill-slate-700 {
  fill: #334155;
}
.fill-slate-800 {
  fill: #1e293b;
}
.fill-slate-900 {
  fill: #0f172a;
}
.fill-slate-950 {
  fill: #020617;
}
.fill-stone-100 {
  fill: #f5f5f4;
}
.fill-stone-200 {
  fill: #e7e5e4;
}
.fill-stone-300 {
  fill: #d6d3d1;
}
.fill-stone-400 {
  fill: #a8a29e;
}
.fill-stone-50 {
  fill: #fafaf9;
}
.fill-stone-500 {
  fill: #78716c;
}
.fill-stone-600 {
  fill: #57534e;
}
.fill-stone-700 {
  fill: #44403c;
}
.fill-stone-800 {
  fill: #292524;
}
.fill-stone-900 {
  fill: #1c1917;
}
.fill-stone-950 {
  fill: #0c0a09;
}
.fill-teal-100 {
  fill: #ccfbf1;
}
.fill-teal-200 {
  fill: #99f6e4;
}
.fill-teal-300 {
  fill: #5eead4;
}
.fill-teal-400 {
  fill: #2dd4bf;
}
.fill-teal-50 {
  fill: #f0fdfa;
}
.fill-teal-500 {
  fill: #14b8a6;
}
.fill-teal-600 {
  fill: #0d9488;
}
.fill-teal-700 {
  fill: #0f766e;
}
.fill-teal-800 {
  fill: #115e59;
}
.fill-teal-900 {
  fill: #134e4a;
}
.fill-teal-950 {
  fill: #042f2e;
}
.fill-tremor-content {
  fill: #6b7280;
}
.fill-tremor-content-emphasis {
  fill: #374151;
}
.fill-violet-100 {
  fill: #ede9fe;
}
.fill-violet-200 {
  fill: #ddd6fe;
}
.fill-violet-300 {
  fill: #c4b5fd;
}
.fill-violet-400 {
  fill: #a78bfa;
}
.fill-violet-50 {
  fill: #f5f3ff;
}
.fill-violet-500 {
  fill: #8b5cf6;
}
.fill-violet-600 {
  fill: #7c3aed;
}
.fill-violet-700 {
  fill: #6d28d9;
}
.fill-violet-800 {
  fill: #5b21b6;
}
.fill-violet-900 {
  fill: #4c1d95;
}
.fill-violet-950 {
  fill: #2e1065;
}
.fill-yellow-100 {
  fill: #fef9c3;
}
.fill-yellow-200 {
  fill: #fef08a;
}
.fill-yellow-300 {
  fill: #fde047;
}
.fill-yellow-400 {
  fill: #facc15;
}
.fill-yellow-50 {
  fill: #fefce8;
}
.fill-yellow-500 {
  fill: #eab308;
}
.fill-yellow-600 {
  fill: #ca8a04;
}
.fill-yellow-700 {
  fill: #a16207;
}
.fill-yellow-800 {
  fill: #854d0e;
}
.fill-yellow-900 {
  fill: #713f12;
}
.fill-yellow-950 {
  fill: #422006;
}
.fill-zinc-100 {
  fill: #f4f4f5;
}
.fill-zinc-200 {
  fill: #e4e4e7;
}
.fill-zinc-300 {
  fill: #d4d4d8;
}
.fill-zinc-400 {
  fill: #a1a1aa;
}
.fill-zinc-50 {
  fill: #fafafa;
}
.fill-zinc-500 {
  fill: #71717a;
}
.fill-zinc-600 {
  fill: #52525b;
}
.fill-zinc-700 {
  fill: #3f3f46;
}
.fill-zinc-800 {
  fill: #27272a;
}
.fill-zinc-900 {
  fill: #18181b;
}
.fill-zinc-950 {
  fill: #09090b;
}
.stroke-amber-100 {
  stroke: #fef3c7;
}
.stroke-amber-200 {
  stroke: #fde68a;
}
.stroke-amber-300 {
  stroke: #fcd34d;
}
.stroke-amber-400 {
  stroke: #fbbf24;
}
.stroke-amber-50 {
  stroke: #fffbeb;
}
.stroke-amber-500 {
  stroke: #f59e0b;
}
.stroke-amber-600 {
  stroke: #d97706;
}
.stroke-amber-700 {
  stroke: #b45309;
}
.stroke-amber-800 {
  stroke: #92400e;
}
.stroke-amber-900 {
  stroke: #78350f;
}
.stroke-amber-950 {
  stroke: #451a03;
}
.stroke-blue-100 {
  stroke: #dbeafe;
}
.stroke-blue-200 {
  stroke: #bfdbfe;
}
.stroke-blue-300 {
  stroke: #93c5fd;
}
.stroke-blue-400 {
  stroke: #60a5fa;
}
.stroke-blue-50 {
  stroke: #eff6ff;
}
.stroke-blue-500 {
  stroke: #3b82f6;
}
.stroke-blue-600 {
  stroke: #2563eb;
}
.stroke-blue-700 {
  stroke: #1d4ed8;
}
.stroke-blue-800 {
  stroke: #1e40af;
}
.stroke-blue-900 {
  stroke: #1e3a8a;
}
.stroke-blue-950 {
  stroke: #172554;
}
.stroke-cyan-100 {
  stroke: #cffafe;
}
.stroke-cyan-200 {
  stroke: #a5f3fc;
}
.stroke-cyan-300 {
  stroke: #67e8f9;
}
.stroke-cyan-400 {
  stroke: #22d3ee;
}
.stroke-cyan-50 {
  stroke: #ecfeff;
}
.stroke-cyan-500 {
  stroke: #06b6d4;
}
.stroke-cyan-600 {
  stroke: #0891b2;
}
.stroke-cyan-700 {
  stroke: #0e7490;
}
.stroke-cyan-800 {
  stroke: #155e75;
}
.stroke-cyan-900 {
  stroke: #164e63;
}
.stroke-cyan-950 {
  stroke: #083344;
}
.stroke-emerald-100 {
  stroke: #d1fae5;
}
.stroke-emerald-200 {
  stroke: #a7f3d0;
}
.stroke-emerald-300 {
  stroke: #6ee7b7;
}
.stroke-emerald-400 {
  stroke: #34d399;
}
.stroke-emerald-50 {
  stroke: #ecfdf5;
}
.stroke-emerald-500 {
  stroke: #10b981;
}
.stroke-emerald-600 {
  stroke: #059669;
}
.stroke-emerald-700 {
  stroke: #047857;
}
.stroke-emerald-800 {
  stroke: #065f46;
}
.stroke-emerald-900 {
  stroke: #064e3b;
}
.stroke-emerald-950 {
  stroke: #022c22;
}
.stroke-fuchsia-100 {
  stroke: #fae8ff;
}
.stroke-fuchsia-200 {
  stroke: #f5d0fe;
}
.stroke-fuchsia-300 {
  stroke: #f0abfc;
}
.stroke-fuchsia-400 {
  stroke: #e879f9;
}
.stroke-fuchsia-50 {
  stroke: #fdf4ff;
}
.stroke-fuchsia-500 {
  stroke: #d946ef;
}
.stroke-fuchsia-600 {
  stroke: #c026d3;
}
.stroke-fuchsia-700 {
  stroke: #a21caf;
}
.stroke-fuchsia-800 {
  stroke: #86198f;
}
.stroke-fuchsia-900 {
  stroke: #701a75;
}
.stroke-fuchsia-950 {
  stroke: #4a044e;
}
.stroke-gray-100 {
  stroke: #f3f4f6;
}
.stroke-gray-200 {
  stroke: #e5e7eb;
}
.stroke-gray-300 {
  stroke: #d1d5db;
}
.stroke-gray-400 {
  stroke: #9ca3af;
}
.stroke-gray-50 {
  stroke: #f9fafb;
}
.stroke-gray-500 {
  stroke: #6b7280;
}
.stroke-gray-600 {
  stroke: #4b5563;
}
.stroke-gray-700 {
  stroke: #374151;
}
.stroke-gray-800 {
  stroke: #1f2937;
}
.stroke-gray-900 {
  stroke: #111827;
}
.stroke-gray-950 {
  stroke: #030712;
}
.stroke-green-100 {
  stroke: #dcfce7;
}
.stroke-green-200 {
  stroke: #bbf7d0;
}
.stroke-green-300 {
  stroke: #86efac;
}
.stroke-green-400 {
  stroke: #4ade80;
}
.stroke-green-50 {
  stroke: #f0fdf4;
}
.stroke-green-500 {
  stroke: #22c55e;
}
.stroke-green-600 {
  stroke: #16a34a;
}
.stroke-green-700 {
  stroke: #15803d;
}
.stroke-green-800 {
  stroke: #166534;
}
.stroke-green-900 {
  stroke: #14532d;
}
.stroke-green-950 {
  stroke: #052e16;
}
.stroke-indigo-100 {
  stroke: #e0e7ff;
}
.stroke-indigo-200 {
  stroke: #c7d2fe;
}
.stroke-indigo-300 {
  stroke: #a5b4fc;
}
.stroke-indigo-400 {
  stroke: #818cf8;
}
.stroke-indigo-50 {
  stroke: #eef2ff;
}
.stroke-indigo-500 {
  stroke: #6366f1;
}
.stroke-indigo-600 {
  stroke: #4f46e5;
}
.stroke-indigo-700 {
  stroke: #4338ca;
}
.stroke-indigo-800 {
  stroke: #3730a3;
}
.stroke-indigo-900 {
  stroke: #312e81;
}
.stroke-indigo-950 {
  stroke: #1e1b4b;
}
.stroke-lime-100 {
  stroke: #ecfccb;
}
.stroke-lime-200 {
  stroke: #d9f99d;
}
.stroke-lime-300 {
  stroke: #bef264;
}
.stroke-lime-400 {
  stroke: #a3e635;
}
.stroke-lime-50 {
  stroke: #f7fee7;
}
.stroke-lime-500 {
  stroke: #84cc16;
}
.stroke-lime-600 {
  stroke: #65a30d;
}
.stroke-lime-700 {
  stroke: #4d7c0f;
}
.stroke-lime-800 {
  stroke: #3f6212;
}
.stroke-lime-900 {
  stroke: #365314;
}
.stroke-lime-950 {
  stroke: #1a2e05;
}
.stroke-neutral-100 {
  stroke: #f5f5f5;
}
.stroke-neutral-200 {
  stroke: #e5e5e5;
}
.stroke-neutral-300 {
  stroke: #d4d4d4;
}
.stroke-neutral-400 {
  stroke: #a3a3a3;
}
.stroke-neutral-50 {
  stroke: #fafafa;
}
.stroke-neutral-500 {
  stroke: #737373;
}
.stroke-neutral-600 {
  stroke: #525252;
}
.stroke-neutral-700 {
  stroke: #404040;
}
.stroke-neutral-800 {
  stroke: #262626;
}
.stroke-neutral-900 {
  stroke: #171717;
}
.stroke-neutral-950 {
  stroke: #0a0a0a;
}
.stroke-orange-100 {
  stroke: #ffedd5;
}
.stroke-orange-200 {
  stroke: #fed7aa;
}
.stroke-orange-300 {
  stroke: #fdba74;
}
.stroke-orange-400 {
  stroke: #fb923c;
}
.stroke-orange-50 {
  stroke: #fff7ed;
}
.stroke-orange-500 {
  stroke: #f97316;
}
.stroke-orange-600 {
  stroke: #ea580c;
}
.stroke-orange-700 {
  stroke: #c2410c;
}
.stroke-orange-800 {
  stroke: #9a3412;
}
.stroke-orange-900 {
  stroke: #7c2d12;
}
.stroke-orange-950 {
  stroke: #431407;
}
.stroke-pink-100 {
  stroke: #fce7f3;
}
.stroke-pink-200 {
  stroke: #fbcfe8;
}
.stroke-pink-300 {
  stroke: #f9a8d4;
}
.stroke-pink-400 {
  stroke: #f472b6;
}
.stroke-pink-50 {
  stroke: #fdf2f8;
}
.stroke-pink-500 {
  stroke: #ec4899;
}
.stroke-pink-600 {
  stroke: #db2777;
}
.stroke-pink-700 {
  stroke: #be185d;
}
.stroke-pink-800 {
  stroke: #9d174d;
}
.stroke-pink-900 {
  stroke: #831843;
}
.stroke-pink-950 {
  stroke: #500724;
}
.stroke-purple-100 {
  stroke: #f3e8ff;
}
.stroke-purple-200 {
  stroke: #e9d5ff;
}
.stroke-purple-300 {
  stroke: #d8b4fe;
}
.stroke-purple-400 {
  stroke: #c084fc;
}
.stroke-purple-50 {
  stroke: #faf5ff;
}
.stroke-purple-500 {
  stroke: #a855f7;
}
.stroke-purple-600 {
  stroke: #9333ea;
}
.stroke-purple-700 {
  stroke: #7e22ce;
}
.stroke-purple-800 {
  stroke: #6b21a8;
}
.stroke-purple-900 {
  stroke: #581c87;
}
.stroke-purple-950 {
  stroke: #3b0764;
}
.stroke-red-100 {
  stroke: #fee2e2;
}
.stroke-red-200 {
  stroke: #fecaca;
}
.stroke-red-300 {
  stroke: #fca5a5;
}
.stroke-red-400 {
  stroke: #f87171;
}
.stroke-red-50 {
  stroke: #fef2f2;
}
.stroke-red-500 {
  stroke: #ef4444;
}
.stroke-red-600 {
  stroke: #dc2626;
}
.stroke-red-700 {
  stroke: #b91c1c;
}
.stroke-red-800 {
  stroke: #991b1b;
}
.stroke-red-900 {
  stroke: #7f1d1d;
}
.stroke-red-950 {
  stroke: #450a0a;
}
.stroke-rose-100 {
  stroke: #ffe4e6;
}
.stroke-rose-200 {
  stroke: #fecdd3;
}
.stroke-rose-300 {
  stroke: #fda4af;
}
.stroke-rose-400 {
  stroke: #fb7185;
}
.stroke-rose-50 {
  stroke: #fff1f2;
}
.stroke-rose-500 {
  stroke: #f43f5e;
}
.stroke-rose-600 {
  stroke: #e11d48;
}
.stroke-rose-700 {
  stroke: #be123c;
}
.stroke-rose-800 {
  stroke: #9f1239;
}
.stroke-rose-900 {
  stroke: #881337;
}
.stroke-rose-950 {
  stroke: #4c0519;
}
.stroke-sky-100 {
  stroke: #e0f2fe;
}
.stroke-sky-200 {
  stroke: #bae6fd;
}
.stroke-sky-300 {
  stroke: #7dd3fc;
}
.stroke-sky-400 {
  stroke: #38bdf8;
}
.stroke-sky-50 {
  stroke: #f0f9ff;
}
.stroke-sky-500 {
  stroke: #0ea5e9;
}
.stroke-sky-600 {
  stroke: #0284c7;
}
.stroke-sky-700 {
  stroke: #0369a1;
}
.stroke-sky-800 {
  stroke: #075985;
}
.stroke-sky-900 {
  stroke: #0c4a6e;
}
.stroke-sky-950 {
  stroke: #082f49;
}
.stroke-slate-100 {
  stroke: #f1f5f9;
}
.stroke-slate-200 {
  stroke: #e2e8f0;
}
.stroke-slate-300 {
  stroke: #cbd5e1;
}
.stroke-slate-400 {
  stroke: #94a3b8;
}
.stroke-slate-50 {
  stroke: #f8fafc;
}
.stroke-slate-500 {
  stroke: #64748b;
}
.stroke-slate-600 {
  stroke: #475569;
}
.stroke-slate-700 {
  stroke: #334155;
}
.stroke-slate-800 {
  stroke: #1e293b;
}
.stroke-slate-900 {
  stroke: #0f172a;
}
.stroke-slate-950 {
  stroke: #020617;
}
.stroke-stone-100 {
  stroke: #f5f5f4;
}
.stroke-stone-200 {
  stroke: #e7e5e4;
}
.stroke-stone-300 {
  stroke: #d6d3d1;
}
.stroke-stone-400 {
  stroke: #a8a29e;
}
.stroke-stone-50 {
  stroke: #fafaf9;
}
.stroke-stone-500 {
  stroke: #78716c;
}
.stroke-stone-600 {
  stroke: #57534e;
}
.stroke-stone-700 {
  stroke: #44403c;
}
.stroke-stone-800 {
  stroke: #292524;
}
.stroke-stone-900 {
  stroke: #1c1917;
}
.stroke-stone-950 {
  stroke: #0c0a09;
}
.stroke-teal-100 {
  stroke: #ccfbf1;
}
.stroke-teal-200 {
  stroke: #99f6e4;
}
.stroke-teal-300 {
  stroke: #5eead4;
}
.stroke-teal-400 {
  stroke: #2dd4bf;
}
.stroke-teal-50 {
  stroke: #f0fdfa;
}
.stroke-teal-500 {
  stroke: #14b8a6;
}
.stroke-teal-600 {
  stroke: #0d9488;
}
.stroke-teal-700 {
  stroke: #0f766e;
}
.stroke-teal-800 {
  stroke: #115e59;
}
.stroke-teal-900 {
  stroke: #134e4a;
}
.stroke-teal-950 {
  stroke: #042f2e;
}
.stroke-tremor-background {
  stroke: #ffffff;
}
.stroke-tremor-border {
  stroke: #e5e7eb;
}
.stroke-tremor-brand {
  stroke: #3b82f6;
}
.stroke-tremor-brand-muted\/50 {
  stroke: rgb(191 219 254 / 0.5);
}
.stroke-violet-100 {
  stroke: #ede9fe;
}
.stroke-violet-200 {
  stroke: #ddd6fe;
}
.stroke-violet-300 {
  stroke: #c4b5fd;
}
.stroke-violet-400 {
  stroke: #a78bfa;
}
.stroke-violet-50 {
  stroke: #f5f3ff;
}
.stroke-violet-500 {
  stroke: #8b5cf6;
}
.stroke-violet-600 {
  stroke: #7c3aed;
}
.stroke-violet-700 {
  stroke: #6d28d9;
}
.stroke-violet-800 {
  stroke: #5b21b6;
}
.stroke-violet-900 {
  stroke: #4c1d95;
}
.stroke-violet-950 {
  stroke: #2e1065;
}
.stroke-yellow-100 {
  stroke: #fef9c3;
}
.stroke-yellow-200 {
  stroke: #fef08a;
}
.stroke-yellow-300 {
  stroke: #fde047;
}
.stroke-yellow-400 {
  stroke: #facc15;
}
.stroke-yellow-50 {
  stroke: #fefce8;
}
.stroke-yellow-500 {
  stroke: #eab308;
}
.stroke-yellow-600 {
  stroke: #ca8a04;
}
.stroke-yellow-700 {
  stroke: #a16207;
}
.stroke-yellow-800 {
  stroke: #854d0e;
}
.stroke-yellow-900 {
  stroke: #713f12;
}
.stroke-yellow-950 {
  stroke: #422006;
}
.stroke-zinc-100 {
  stroke: #f4f4f5;
}
.stroke-zinc-200 {
  stroke: #e4e4e7;
}
.stroke-zinc-300 {
  stroke: #d4d4d8;
}
.stroke-zinc-400 {
  stroke: #a1a1aa;
}
.stroke-zinc-50 {
  stroke: #fafafa;
}
.stroke-zinc-500 {
  stroke: #71717a;
}
.stroke-zinc-600 {
  stroke: #52525b;
}
.stroke-zinc-700 {
  stroke: #3f3f46;
}
.stroke-zinc-800 {
  stroke: #27272a;
}
.stroke-zinc-900 {
  stroke: #18181b;
}
.stroke-zinc-950 {
  stroke: #09090b;
}
.stroke-1 {
  stroke-width: 1;
}
.p-0 {
  padding: 0px;
}
.p-0\.5 {
  padding: 0.125rem;
}
.p-1 {
  padding: 0.25rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-3\.5 {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.py-\[10px\] {
  padding-top: 10px;
  padding-bottom: 10px;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-11 {
  padding-left: 2.75rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-2\.5 {
  padding-left: 0.625rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pr-1 {
  padding-right: 0.25rem;
}
.pr-1\.5 {
  padding-right: 0.375rem;
}
.pr-12 {
  padding-right: 3rem;
}
.pr-14 {
  padding-right: 3.5rem;
}
.pr-16 {
  padding-right: 4rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-2\.5 {
  padding-right: 0.625rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-8 {
  padding-right: 2rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.align-top {
  vertical-align: top;
}
.align-middle {
  vertical-align: middle;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.capitalize {
  text-transform: capitalize;
}
.italic {
  font-style: italic;
}
.tabular-nums {
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}
.leading-none {
  line-height: 1;
}
.\!text-tremor-content-subtle {
  --tw-text-opacity: 1 !important;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1)) !important;
}
.text-\[\#d1d5db\]\/15 {
  color: rgb(209 213 219 / 0.15);
}
.text-amber-100 {
  --tw-text-opacity: 1;
  color: rgb(254 243 199 / var(--tw-text-opacity, 1));
}
.text-amber-200 {
  --tw-text-opacity: 1;
  color: rgb(253 230 138 / var(--tw-text-opacity, 1));
}
.text-amber-300 {
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}
.text-amber-400 {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}
.text-amber-50 {
  --tw-text-opacity: 1;
  color: rgb(255 251 235 / var(--tw-text-opacity, 1));
}
.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}
.text-amber-600 {
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}
.text-amber-700 {
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity, 1));
}
.text-amber-800 {
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}
.text-amber-900 {
  --tw-text-opacity: 1;
  color: rgb(120 53 15 / var(--tw-text-opacity, 1));
}
.text-amber-950 {
  --tw-text-opacity: 1;
  color: rgb(69 26 3 / var(--tw-text-opacity, 1));
}
.text-blue-100 {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.text-blue-200 {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}
.text-blue-300 {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-blue-50 {
  --tw-text-opacity: 1;
  color: rgb(239 246 255 / var(--tw-text-opacity, 1));
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}
.text-blue-950 {
  --tw-text-opacity: 1;
  color: rgb(23 37 84 / var(--tw-text-opacity, 1));
}
.text-cyan-100 {
  --tw-text-opacity: 1;
  color: rgb(207 250 254 / var(--tw-text-opacity, 1));
}
.text-cyan-200 {
  --tw-text-opacity: 1;
  color: rgb(165 243 252 / var(--tw-text-opacity, 1));
}
.text-cyan-300 {
  --tw-text-opacity: 1;
  color: rgb(103 232 249 / var(--tw-text-opacity, 1));
}
.text-cyan-400 {
  --tw-text-opacity: 1;
  color: rgb(34 211 238 / var(--tw-text-opacity, 1));
}
.text-cyan-50 {
  --tw-text-opacity: 1;
  color: rgb(236 254 255 / var(--tw-text-opacity, 1));
}
.text-cyan-500 {
  --tw-text-opacity: 1;
  color: rgb(6 182 212 / var(--tw-text-opacity, 1));
}
.text-cyan-600 {
  --tw-text-opacity: 1;
  color: rgb(8 145 178 / var(--tw-text-opacity, 1));
}
.text-cyan-700 {
  --tw-text-opacity: 1;
  color: rgb(14 116 144 / var(--tw-text-opacity, 1));
}
.text-cyan-800 {
  --tw-text-opacity: 1;
  color: rgb(21 94 117 / var(--tw-text-opacity, 1));
}
.text-cyan-900 {
  --tw-text-opacity: 1;
  color: rgb(22 78 99 / var(--tw-text-opacity, 1));
}
.text-cyan-950 {
  --tw-text-opacity: 1;
  color: rgb(8 51 68 / var(--tw-text-opacity, 1));
}
.text-emerald-100 {
  --tw-text-opacity: 1;
  color: rgb(209 250 229 / var(--tw-text-opacity, 1));
}
.text-emerald-200 {
  --tw-text-opacity: 1;
  color: rgb(167 243 208 / var(--tw-text-opacity, 1));
}
.text-emerald-300 {
  --tw-text-opacity: 1;
  color: rgb(110 231 183 / var(--tw-text-opacity, 1));
}
.text-emerald-400 {
  --tw-text-opacity: 1;
  color: rgb(52 211 153 / var(--tw-text-opacity, 1));
}
.text-emerald-50 {
  --tw-text-opacity: 1;
  color: rgb(236 253 245 / var(--tw-text-opacity, 1));
}
.text-emerald-500 {
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}
.text-emerald-600 {
  --tw-text-opacity: 1;
  color: rgb(5 150 105 / var(--tw-text-opacity, 1));
}
.text-emerald-700 {
  --tw-text-opacity: 1;
  color: rgb(4 120 87 / var(--tw-text-opacity, 1));
}
.text-emerald-800 {
  --tw-text-opacity: 1;
  color: rgb(6 95 70 / var(--tw-text-opacity, 1));
}
.text-emerald-900 {
  --tw-text-opacity: 1;
  color: rgb(6 78 59 / var(--tw-text-opacity, 1));
}
.text-emerald-950 {
  --tw-text-opacity: 1;
  color: rgb(2 44 34 / var(--tw-text-opacity, 1));
}
.text-fuchsia-100 {
  --tw-text-opacity: 1;
  color: rgb(250 232 255 / var(--tw-text-opacity, 1));
}
.text-fuchsia-200 {
  --tw-text-opacity: 1;
  color: rgb(245 208 254 / var(--tw-text-opacity, 1));
}
.text-fuchsia-300 {
  --tw-text-opacity: 1;
  color: rgb(240 171 252 / var(--tw-text-opacity, 1));
}
.text-fuchsia-400 {
  --tw-text-opacity: 1;
  color: rgb(232 121 249 / var(--tw-text-opacity, 1));
}
.text-fuchsia-50 {
  --tw-text-opacity: 1;
  color: rgb(253 244 255 / var(--tw-text-opacity, 1));
}
.text-fuchsia-500 {
  --tw-text-opacity: 1;
  color: rgb(217 70 239 / var(--tw-text-opacity, 1));
}
.text-fuchsia-600 {
  --tw-text-opacity: 1;
  color: rgb(192 38 211 / var(--tw-text-opacity, 1));
}
.text-fuchsia-700 {
  --tw-text-opacity: 1;
  color: rgb(162 28 175 / var(--tw-text-opacity, 1));
}
.text-fuchsia-800 {
  --tw-text-opacity: 1;
  color: rgb(134 25 143 / var(--tw-text-opacity, 1));
}
.text-fuchsia-900 {
  --tw-text-opacity: 1;
  color: rgb(112 26 117 / var(--tw-text-opacity, 1));
}
.text-fuchsia-950 {
  --tw-text-opacity: 1;
  color: rgb(74 4 78 / var(--tw-text-opacity, 1));
}
.text-gray-100 {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}
.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-50 {
  --tw-text-opacity: 1;
  color: rgb(249 250 251 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-gray-950 {
  --tw-text-opacity: 1;
  color: rgb(3 7 18 / var(--tw-text-opacity, 1));
}
.text-green-100 {
  --tw-text-opacity: 1;
  color: rgb(220 252 231 / var(--tw-text-opacity, 1));
}
.text-green-200 {
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}
.text-green-300 {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}
.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-green-50 {
  --tw-text-opacity: 1;
  color: rgb(240 253 244 / var(--tw-text-opacity, 1));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-green-900 {
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}
.text-green-950 {
  --tw-text-opacity: 1;
  color: rgb(5 46 22 / var(--tw-text-opacity, 1));
}
.text-indigo-100 {
  --tw-text-opacity: 1;
  color: rgb(224 231 255 / var(--tw-text-opacity, 1));
}
.text-indigo-200 {
  --tw-text-opacity: 1;
  color: rgb(199 210 254 / var(--tw-text-opacity, 1));
}
.text-indigo-300 {
  --tw-text-opacity: 1;
  color: rgb(165 180 252 / var(--tw-text-opacity, 1));
}
.text-indigo-400 {
  --tw-text-opacity: 1;
  color: rgb(129 140 248 / var(--tw-text-opacity, 1));
}
.text-indigo-50 {
  --tw-text-opacity: 1;
  color: rgb(238 242 255 / var(--tw-text-opacity, 1));
}
.text-indigo-500 {
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}
.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}
.text-indigo-700 {
  --tw-text-opacity: 1;
  color: rgb(67 56 202 / var(--tw-text-opacity, 1));
}
.text-indigo-800 {
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}
.text-indigo-900 {
  --tw-text-opacity: 1;
  color: rgb(49 46 129 / var(--tw-text-opacity, 1));
}
.text-indigo-950 {
  --tw-text-opacity: 1;
  color: rgb(30 27 75 / var(--tw-text-opacity, 1));
}
.text-inherit {
  color: inherit;
}
.text-lime-100 {
  --tw-text-opacity: 1;
  color: rgb(236 252 203 / var(--tw-text-opacity, 1));
}
.text-lime-200 {
  --tw-text-opacity: 1;
  color: rgb(217 249 157 / var(--tw-text-opacity, 1));
}
.text-lime-300 {
  --tw-text-opacity: 1;
  color: rgb(190 242 100 / var(--tw-text-opacity, 1));
}
.text-lime-400 {
  --tw-text-opacity: 1;
  color: rgb(163 230 53 / var(--tw-text-opacity, 1));
}
.text-lime-50 {
  --tw-text-opacity: 1;
  color: rgb(247 254 231 / var(--tw-text-opacity, 1));
}
.text-lime-500 {
  --tw-text-opacity: 1;
  color: rgb(132 204 22 / var(--tw-text-opacity, 1));
}
.text-lime-600 {
  --tw-text-opacity: 1;
  color: rgb(101 163 13 / var(--tw-text-opacity, 1));
}
.text-lime-700 {
  --tw-text-opacity: 1;
  color: rgb(77 124 15 / var(--tw-text-opacity, 1));
}
.text-lime-800 {
  --tw-text-opacity: 1;
  color: rgb(63 98 18 / var(--tw-text-opacity, 1));
}
.text-lime-900 {
  --tw-text-opacity: 1;
  color: rgb(54 83 20 / var(--tw-text-opacity, 1));
}
.text-lime-950 {
  --tw-text-opacity: 1;
  color: rgb(26 46 5 / var(--tw-text-opacity, 1));
}
.text-neutral-100 {
  --tw-text-opacity: 1;
  color: rgb(245 245 245 / var(--tw-text-opacity, 1));
}
.text-neutral-200 {
  --tw-text-opacity: 1;
  color: rgb(229 229 229 / var(--tw-text-opacity, 1));
}
.text-neutral-300 {
  --tw-text-opacity: 1;
  color: rgb(212 212 212 / var(--tw-text-opacity, 1));
}
.text-neutral-400 {
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity, 1));
}
.text-neutral-50 {
  --tw-text-opacity: 1;
  color: rgb(250 250 250 / var(--tw-text-opacity, 1));
}
.text-neutral-500 {
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}
.text-neutral-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}
.text-neutral-700 {
  --tw-text-opacity: 1;
  color: rgb(64 64 64 / var(--tw-text-opacity, 1));
}
.text-neutral-800 {
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
}
.text-neutral-900 {
  --tw-text-opacity: 1;
  color: rgb(23 23 23 / var(--tw-text-opacity, 1));
}
.text-neutral-950 {
  --tw-text-opacity: 1;
  color: rgb(10 10 10 / var(--tw-text-opacity, 1));
}
.text-orange-100 {
  --tw-text-opacity: 1;
  color: rgb(255 237 213 / var(--tw-text-opacity, 1));
}
.text-orange-200 {
  --tw-text-opacity: 1;
  color: rgb(254 215 170 / var(--tw-text-opacity, 1));
}
.text-orange-300 {
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}
.text-orange-400 {
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}
.text-orange-50 {
  --tw-text-opacity: 1;
  color: rgb(255 247 237 / var(--tw-text-opacity, 1));
}
.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}
.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-orange-700 {
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}
.text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}
.text-orange-900 {
  --tw-text-opacity: 1;
  color: rgb(124 45 18 / var(--tw-text-opacity, 1));
}
.text-orange-950 {
  --tw-text-opacity: 1;
  color: rgb(67 20 7 / var(--tw-text-opacity, 1));
}
.text-pink-100 {
  --tw-text-opacity: 1;
  color: rgb(252 231 243 / var(--tw-text-opacity, 1));
}
.text-pink-200 {
  --tw-text-opacity: 1;
  color: rgb(251 207 232 / var(--tw-text-opacity, 1));
}
.text-pink-300 {
  --tw-text-opacity: 1;
  color: rgb(249 168 212 / var(--tw-text-opacity, 1));
}
.text-pink-400 {
  --tw-text-opacity: 1;
  color: rgb(244 114 182 / var(--tw-text-opacity, 1));
}
.text-pink-50 {
  --tw-text-opacity: 1;
  color: rgb(253 242 248 / var(--tw-text-opacity, 1));
}
.text-pink-500 {
  --tw-text-opacity: 1;
  color: rgb(236 72 153 / var(--tw-text-opacity, 1));
}
.text-pink-600 {
  --tw-text-opacity: 1;
  color: rgb(219 39 119 / var(--tw-text-opacity, 1));
}
.text-pink-700 {
  --tw-text-opacity: 1;
  color: rgb(190 24 93 / var(--tw-text-opacity, 1));
}
.text-pink-800 {
  --tw-text-opacity: 1;
  color: rgb(157 23 77 / var(--tw-text-opacity, 1));
}
.text-pink-900 {
  --tw-text-opacity: 1;
  color: rgb(131 24 67 / var(--tw-text-opacity, 1));
}
.text-pink-950 {
  --tw-text-opacity: 1;
  color: rgb(80 7 36 / var(--tw-text-opacity, 1));
}
.text-purple-100 {
  --tw-text-opacity: 1;
  color: rgb(243 232 255 / var(--tw-text-opacity, 1));
}
.text-purple-200 {
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}
.text-purple-300 {
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}
.text-purple-400 {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.text-purple-50 {
  --tw-text-opacity: 1;
  color: rgb(250 245 255 / var(--tw-text-opacity, 1));
}
.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.text-purple-700 {
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.text-purple-900 {
  --tw-text-opacity: 1;
  color: rgb(88 28 135 / var(--tw-text-opacity, 1));
}
.text-purple-950 {
  --tw-text-opacity: 1;
  color: rgb(59 7 100 / var(--tw-text-opacity, 1));
}
.text-red-100 {
  --tw-text-opacity: 1;
  color: rgb(254 226 226 / var(--tw-text-opacity, 1));
}
.text-red-200 {
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}
.text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-red-50 {
  --tw-text-opacity: 1;
  color: rgb(254 242 242 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-red-900 {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}
.text-red-950 {
  --tw-text-opacity: 1;
  color: rgb(69 10 10 / var(--tw-text-opacity, 1));
}
.text-rose-100 {
  --tw-text-opacity: 1;
  color: rgb(255 228 230 / var(--tw-text-opacity, 1));
}
.text-rose-200 {
  --tw-text-opacity: 1;
  color: rgb(254 205 211 / var(--tw-text-opacity, 1));
}
.text-rose-300 {
  --tw-text-opacity: 1;
  color: rgb(253 164 175 / var(--tw-text-opacity, 1));
}
.text-rose-400 {
  --tw-text-opacity: 1;
  color: rgb(251 113 133 / var(--tw-text-opacity, 1));
}
.text-rose-50 {
  --tw-text-opacity: 1;
  color: rgb(255 241 242 / var(--tw-text-opacity, 1));
}
.text-rose-500 {
  --tw-text-opacity: 1;
  color: rgb(244 63 94 / var(--tw-text-opacity, 1));
}
.text-rose-600 {
  --tw-text-opacity: 1;
  color: rgb(225 29 72 / var(--tw-text-opacity, 1));
}
.text-rose-700 {
  --tw-text-opacity: 1;
  color: rgb(190 18 60 / var(--tw-text-opacity, 1));
}
.text-rose-800 {
  --tw-text-opacity: 1;
  color: rgb(159 18 57 / var(--tw-text-opacity, 1));
}
.text-rose-900 {
  --tw-text-opacity: 1;
  color: rgb(136 19 55 / var(--tw-text-opacity, 1));
}
.text-rose-950 {
  --tw-text-opacity: 1;
  color: rgb(76 5 25 / var(--tw-text-opacity, 1));
}
.text-sky-100 {
  --tw-text-opacity: 1;
  color: rgb(224 242 254 / var(--tw-text-opacity, 1));
}
.text-sky-200 {
  --tw-text-opacity: 1;
  color: rgb(186 230 253 / var(--tw-text-opacity, 1));
}
.text-sky-300 {
  --tw-text-opacity: 1;
  color: rgb(125 211 252 / var(--tw-text-opacity, 1));
}
.text-sky-400 {
  --tw-text-opacity: 1;
  color: rgb(56 189 248 / var(--tw-text-opacity, 1));
}
.text-sky-50 {
  --tw-text-opacity: 1;
  color: rgb(240 249 255 / var(--tw-text-opacity, 1));
}
.text-sky-500 {
  --tw-text-opacity: 1;
  color: rgb(14 165 233 / var(--tw-text-opacity, 1));
}
.text-sky-600 {
  --tw-text-opacity: 1;
  color: rgb(2 132 199 / var(--tw-text-opacity, 1));
}
.text-sky-700 {
  --tw-text-opacity: 1;
  color: rgb(3 105 161 / var(--tw-text-opacity, 1));
}
.text-sky-800 {
  --tw-text-opacity: 1;
  color: rgb(7 89 133 / var(--tw-text-opacity, 1));
}
.text-sky-900 {
  --tw-text-opacity: 1;
  color: rgb(12 74 110 / var(--tw-text-opacity, 1));
}
.text-sky-950 {
  --tw-text-opacity: 1;
  color: rgb(8 47 73 / var(--tw-text-opacity, 1));
}
.text-slate-100 {
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity, 1));
}
.text-slate-200 {
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
}
.text-slate-300 {
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}
.text-slate-400 {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}
.text-slate-50 {
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity, 1));
}
.text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.text-slate-600 {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.text-slate-700 {
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}
.text-slate-800 {
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity, 1));
}
.text-slate-900 {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}
.text-slate-950 {
  --tw-text-opacity: 1;
  color: rgb(2 6 23 / var(--tw-text-opacity, 1));
}
.text-stone-100 {
  --tw-text-opacity: 1;
  color: rgb(245 245 244 / var(--tw-text-opacity, 1));
}
.text-stone-200 {
  --tw-text-opacity: 1;
  color: rgb(231 229 228 / var(--tw-text-opacity, 1));
}
.text-stone-300 {
  --tw-text-opacity: 1;
  color: rgb(214 211 209 / var(--tw-text-opacity, 1));
}
.text-stone-400 {
  --tw-text-opacity: 1;
  color: rgb(168 162 158 / var(--tw-text-opacity, 1));
}
.text-stone-50 {
  --tw-text-opacity: 1;
  color: rgb(250 250 249 / var(--tw-text-opacity, 1));
}
.text-stone-500 {
  --tw-text-opacity: 1;
  color: rgb(120 113 108 / var(--tw-text-opacity, 1));
}
.text-stone-600 {
  --tw-text-opacity: 1;
  color: rgb(87 83 78 / var(--tw-text-opacity, 1));
}
.text-stone-700 {
  --tw-text-opacity: 1;
  color: rgb(68 64 60 / var(--tw-text-opacity, 1));
}
.text-stone-800 {
  --tw-text-opacity: 1;
  color: rgb(41 37 36 / var(--tw-text-opacity, 1));
}
.text-stone-900 {
  --tw-text-opacity: 1;
  color: rgb(28 25 23 / var(--tw-text-opacity, 1));
}
.text-stone-950 {
  --tw-text-opacity: 1;
  color: rgb(12 10 9 / var(--tw-text-opacity, 1));
}
.text-teal-100 {
  --tw-text-opacity: 1;
  color: rgb(204 251 241 / var(--tw-text-opacity, 1));
}
.text-teal-200 {
  --tw-text-opacity: 1;
  color: rgb(153 246 228 / var(--tw-text-opacity, 1));
}
.text-teal-300 {
  --tw-text-opacity: 1;
  color: rgb(94 234 212 / var(--tw-text-opacity, 1));
}
.text-teal-400 {
  --tw-text-opacity: 1;
  color: rgb(45 212 191 / var(--tw-text-opacity, 1));
}
.text-teal-50 {
  --tw-text-opacity: 1;
  color: rgb(240 253 250 / var(--tw-text-opacity, 1));
}
.text-teal-500 {
  --tw-text-opacity: 1;
  color: rgb(20 184 166 / var(--tw-text-opacity, 1));
}
.text-teal-600 {
  --tw-text-opacity: 1;
  color: rgb(13 148 136 / var(--tw-text-opacity, 1));
}
.text-teal-700 {
  --tw-text-opacity: 1;
  color: rgb(15 118 110 / var(--tw-text-opacity, 1));
}
.text-teal-800 {
  --tw-text-opacity: 1;
  color: rgb(17 94 89 / var(--tw-text-opacity, 1));
}
.text-teal-900 {
  --tw-text-opacity: 1;
  color: rgb(19 78 74 / var(--tw-text-opacity, 1));
}
.text-teal-950 {
  --tw-text-opacity: 1;
  color: rgb(4 47 46 / var(--tw-text-opacity, 1));
}
.text-transparent {
  color: transparent;
}
.text-tremor-brand {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-tremor-brand-emphasis {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-tremor-brand-inverted {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-tremor-content {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-tremor-content-emphasis {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-tremor-content-strong {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-tremor-content-subtle {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-violet-100 {
  --tw-text-opacity: 1;
  color: rgb(237 233 254 / var(--tw-text-opacity, 1));
}
.text-violet-200 {
  --tw-text-opacity: 1;
  color: rgb(221 214 254 / var(--tw-text-opacity, 1));
}
.text-violet-300 {
  --tw-text-opacity: 1;
  color: rgb(196 181 253 / var(--tw-text-opacity, 1));
}
.text-violet-400 {
  --tw-text-opacity: 1;
  color: rgb(167 139 250 / var(--tw-text-opacity, 1));
}
.text-violet-50 {
  --tw-text-opacity: 1;
  color: rgb(245 243 255 / var(--tw-text-opacity, 1));
}
.text-violet-500 {
  --tw-text-opacity: 1;
  color: rgb(139 92 246 / var(--tw-text-opacity, 1));
}
.text-violet-600 {
  --tw-text-opacity: 1;
  color: rgb(124 58 237 / var(--tw-text-opacity, 1));
}
.text-violet-700 {
  --tw-text-opacity: 1;
  color: rgb(109 40 217 / var(--tw-text-opacity, 1));
}
.text-violet-800 {
  --tw-text-opacity: 1;
  color: rgb(91 33 182 / var(--tw-text-opacity, 1));
}
.text-violet-900 {
  --tw-text-opacity: 1;
  color: rgb(76 29 149 / var(--tw-text-opacity, 1));
}
.text-violet-950 {
  --tw-text-opacity: 1;
  color: rgb(46 16 101 / var(--tw-text-opacity, 1));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-100 {
  --tw-text-opacity: 1;
  color: rgb(254 249 195 / var(--tw-text-opacity, 1));
}
.text-yellow-200 {
  --tw-text-opacity: 1;
  color: rgb(254 240 138 / var(--tw-text-opacity, 1));
}
.text-yellow-300 {
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}
.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-50 {
  --tw-text-opacity: 1;
  color: rgb(254 252 232 / var(--tw-text-opacity, 1));
}
.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}
.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.text-yellow-900 {
  --tw-text-opacity: 1;
  color: rgb(113 63 18 / var(--tw-text-opacity, 1));
}
.text-yellow-950 {
  --tw-text-opacity: 1;
  color: rgb(66 32 6 / var(--tw-text-opacity, 1));
}
.text-zinc-100 {
  --tw-text-opacity: 1;
  color: rgb(244 244 245 / var(--tw-text-opacity, 1));
}
.text-zinc-200 {
  --tw-text-opacity: 1;
  color: rgb(228 228 231 / var(--tw-text-opacity, 1));
}
.text-zinc-300 {
  --tw-text-opacity: 1;
  color: rgb(212 212 216 / var(--tw-text-opacity, 1));
}
.text-zinc-400 {
  --tw-text-opacity: 1;
  color: rgb(161 161 170 / var(--tw-text-opacity, 1));
}
.text-zinc-50 {
  --tw-text-opacity: 1;
  color: rgb(250 250 250 / var(--tw-text-opacity, 1));
}
.text-zinc-500 {
  --tw-text-opacity: 1;
  color: rgb(113 113 122 / var(--tw-text-opacity, 1));
}
.text-zinc-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 91 / var(--tw-text-opacity, 1));
}
.text-zinc-700 {
  --tw-text-opacity: 1;
  color: rgb(63 63 70 / var(--tw-text-opacity, 1));
}
.text-zinc-800 {
  --tw-text-opacity: 1;
  color: rgb(39 39 42 / var(--tw-text-opacity, 1));
}
.text-zinc-900 {
  --tw-text-opacity: 1;
  color: rgb(24 24 27 / var(--tw-text-opacity, 1));
}
.text-zinc-950 {
  --tw-text-opacity: 1;
  color: rgb(9 9 11 / var(--tw-text-opacity, 1));
}
.accent-tremor-brand {
  accent-color: #3b82f6;
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.opacity-20 {
  opacity: 0.2;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-40 {
  opacity: 0.4;
}
.opacity-50 {
  opacity: 0.5;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.outline-tremor-brand {
  outline-color: #3b82f6;
}
.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-inset {
  --tw-ring-inset: inset;
}
.ring-amber-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 243 199 / var(--tw-ring-opacity, 1));
}
.ring-amber-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(253 230 138 / var(--tw-ring-opacity, 1));
}
.ring-amber-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(252 211 77 / var(--tw-ring-opacity, 1));
}
.ring-amber-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(251 191 36 / var(--tw-ring-opacity, 1));
}
.ring-amber-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 251 235 / var(--tw-ring-opacity, 1));
}
.ring-amber-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(245 158 11 / var(--tw-ring-opacity, 1));
}
.ring-amber-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(217 119 6 / var(--tw-ring-opacity, 1));
}
.ring-amber-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(180 83 9 / var(--tw-ring-opacity, 1));
}
.ring-amber-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(146 64 14 / var(--tw-ring-opacity, 1));
}
.ring-amber-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(120 53 15 / var(--tw-ring-opacity, 1));
}
.ring-amber-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(69 26 3 / var(--tw-ring-opacity, 1));
}
.ring-blue-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(219 234 254 / var(--tw-ring-opacity, 1));
}
.ring-blue-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(191 219 254 / var(--tw-ring-opacity, 1));
}
.ring-blue-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity, 1));
}
.ring-blue-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(96 165 250 / var(--tw-ring-opacity, 1));
}
.ring-blue-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 246 255 / var(--tw-ring-opacity, 1));
}
.ring-blue-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}
.ring-blue-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(37 99 235 / var(--tw-ring-opacity, 1));
}
.ring-blue-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(29 78 216 / var(--tw-ring-opacity, 1));
}
.ring-blue-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity, 1));
}
.ring-blue-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 58 138 / var(--tw-ring-opacity, 1));
}
.ring-blue-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(23 37 84 / var(--tw-ring-opacity, 1));
}
.ring-cyan-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(207 250 254 / var(--tw-ring-opacity, 1));
}
.ring-cyan-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(165 243 252 / var(--tw-ring-opacity, 1));
}
.ring-cyan-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(103 232 249 / var(--tw-ring-opacity, 1));
}
.ring-cyan-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 211 238 / var(--tw-ring-opacity, 1));
}
.ring-cyan-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(236 254 255 / var(--tw-ring-opacity, 1));
}
.ring-cyan-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(6 182 212 / var(--tw-ring-opacity, 1));
}
.ring-cyan-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(8 145 178 / var(--tw-ring-opacity, 1));
}
.ring-cyan-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 116 144 / var(--tw-ring-opacity, 1));
}
.ring-cyan-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(21 94 117 / var(--tw-ring-opacity, 1));
}
.ring-cyan-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(22 78 99 / var(--tw-ring-opacity, 1));
}
.ring-cyan-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(8 51 68 / var(--tw-ring-opacity, 1));
}
.ring-emerald-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 250 229 / var(--tw-ring-opacity, 1));
}
.ring-emerald-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(167 243 208 / var(--tw-ring-opacity, 1));
}
.ring-emerald-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(110 231 183 / var(--tw-ring-opacity, 1));
}
.ring-emerald-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(52 211 153 / var(--tw-ring-opacity, 1));
}
.ring-emerald-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(236 253 245 / var(--tw-ring-opacity, 1));
}
.ring-emerald-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(16 185 129 / var(--tw-ring-opacity, 1));
}
.ring-emerald-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(5 150 105 / var(--tw-ring-opacity, 1));
}
.ring-emerald-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(4 120 87 / var(--tw-ring-opacity, 1));
}
.ring-emerald-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(6 95 70 / var(--tw-ring-opacity, 1));
}
.ring-emerald-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(6 78 59 / var(--tw-ring-opacity, 1));
}
.ring-emerald-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(2 44 34 / var(--tw-ring-opacity, 1));
}
.ring-fuchsia-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 232 255 / var(--tw-ring-opacity, 1));
}
.ring-fuchsia-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(245 208 254 / var(--tw-ring-opacity, 1));
}
.ring-fuchsia-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(240 171 252 / var(--tw-ring-opacity, 1));
}
.ring-fuchsia-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(232 121 249 / var(--tw-ring-opacity, 1));
}
.ring-fuchsia-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(253 244 255 / var(--tw-ring-opacity, 1));
}
.ring-fuchsia-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(217 70 239 / var(--tw-ring-opacity, 1));
}
.ring-fuchsia-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(192 38 211 / var(--tw-ring-opacity, 1));
}
.ring-fuchsia-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(162 28 175 / var(--tw-ring-opacity, 1));
}
.ring-fuchsia-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(134 25 143 / var(--tw-ring-opacity, 1));
}
.ring-fuchsia-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(112 26 117 / var(--tw-ring-opacity, 1));
}
.ring-fuchsia-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(74 4 78 / var(--tw-ring-opacity, 1));
}
.ring-gray-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(243 244 246 / var(--tw-ring-opacity, 1));
}
.ring-gray-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity, 1));
}
.ring-gray-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity, 1));
}
.ring-gray-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(156 163 175 / var(--tw-ring-opacity, 1));
}
.ring-gray-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 250 251 / var(--tw-ring-opacity, 1));
}
.ring-gray-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity, 1));
}
.ring-gray-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(75 85 99 / var(--tw-ring-opacity, 1));
}
.ring-gray-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(55 65 81 / var(--tw-ring-opacity, 1));
}
.ring-gray-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(31 41 55 / var(--tw-ring-opacity, 1));
}
.ring-gray-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 24 39 / var(--tw-ring-opacity, 1));
}
.ring-gray-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(3 7 18 / var(--tw-ring-opacity, 1));
}
.ring-green-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(220 252 231 / var(--tw-ring-opacity, 1));
}
.ring-green-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(187 247 208 / var(--tw-ring-opacity, 1));
}
.ring-green-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(134 239 172 / var(--tw-ring-opacity, 1));
}
.ring-green-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(74 222 128 / var(--tw-ring-opacity, 1));
}
.ring-green-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(240 253 244 / var(--tw-ring-opacity, 1));
}
.ring-green-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}
.ring-green-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(22 163 74 / var(--tw-ring-opacity, 1));
}
.ring-green-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(21 128 61 / var(--tw-ring-opacity, 1));
}
.ring-green-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(22 101 52 / var(--tw-ring-opacity, 1));
}
.ring-green-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(20 83 45 / var(--tw-ring-opacity, 1));
}
.ring-green-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(5 46 22 / var(--tw-ring-opacity, 1));
}
.ring-indigo-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(224 231 255 / var(--tw-ring-opacity, 1));
}
.ring-indigo-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(199 210 254 / var(--tw-ring-opacity, 1));
}
.ring-indigo-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(165 180 252 / var(--tw-ring-opacity, 1));
}
.ring-indigo-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(129 140 248 / var(--tw-ring-opacity, 1));
}
.ring-indigo-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(238 242 255 / var(--tw-ring-opacity, 1));
}
.ring-indigo-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}
.ring-indigo-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(79 70 229 / var(--tw-ring-opacity, 1));
}
.ring-indigo-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(67 56 202 / var(--tw-ring-opacity, 1));
}
.ring-indigo-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(55 48 163 / var(--tw-ring-opacity, 1));
}
.ring-indigo-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(49 46 129 / var(--tw-ring-opacity, 1));
}
.ring-indigo-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 27 75 / var(--tw-ring-opacity, 1));
}
.ring-lime-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(236 252 203 / var(--tw-ring-opacity, 1));
}
.ring-lime-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(217 249 157 / var(--tw-ring-opacity, 1));
}
.ring-lime-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(190 242 100 / var(--tw-ring-opacity, 1));
}
.ring-lime-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(163 230 53 / var(--tw-ring-opacity, 1));
}
.ring-lime-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(247 254 231 / var(--tw-ring-opacity, 1));
}
.ring-lime-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(132 204 22 / var(--tw-ring-opacity, 1));
}
.ring-lime-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(101 163 13 / var(--tw-ring-opacity, 1));
}
.ring-lime-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(77 124 15 / var(--tw-ring-opacity, 1));
}
.ring-lime-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(63 98 18 / var(--tw-ring-opacity, 1));
}
.ring-lime-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(54 83 20 / var(--tw-ring-opacity, 1));
}
.ring-lime-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(26 46 5 / var(--tw-ring-opacity, 1));
}
.ring-neutral-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(245 245 245 / var(--tw-ring-opacity, 1));
}
.ring-neutral-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(229 229 229 / var(--tw-ring-opacity, 1));
}
.ring-neutral-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(212 212 212 / var(--tw-ring-opacity, 1));
}
.ring-neutral-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(163 163 163 / var(--tw-ring-opacity, 1));
}
.ring-neutral-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 250 250 / var(--tw-ring-opacity, 1));
}
.ring-neutral-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(115 115 115 / var(--tw-ring-opacity, 1));
}
.ring-neutral-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(82 82 82 / var(--tw-ring-opacity, 1));
}
.ring-neutral-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(64 64 64 / var(--tw-ring-opacity, 1));
}
.ring-neutral-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(38 38 38 / var(--tw-ring-opacity, 1));
}
.ring-neutral-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(23 23 23 / var(--tw-ring-opacity, 1));
}
.ring-neutral-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(10 10 10 / var(--tw-ring-opacity, 1));
}
.ring-orange-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 237 213 / var(--tw-ring-opacity, 1));
}
.ring-orange-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 215 170 / var(--tw-ring-opacity, 1));
}
.ring-orange-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(253 186 116 / var(--tw-ring-opacity, 1));
}
.ring-orange-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(251 146 60 / var(--tw-ring-opacity, 1));
}
.ring-orange-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 247 237 / var(--tw-ring-opacity, 1));
}
.ring-orange-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));
}
.ring-orange-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(234 88 12 / var(--tw-ring-opacity, 1));
}
.ring-orange-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(194 65 12 / var(--tw-ring-opacity, 1));
}
.ring-orange-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(154 52 18 / var(--tw-ring-opacity, 1));
}
.ring-orange-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(124 45 18 / var(--tw-ring-opacity, 1));
}
.ring-orange-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(67 20 7 / var(--tw-ring-opacity, 1));
}
.ring-pink-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(252 231 243 / var(--tw-ring-opacity, 1));
}
.ring-pink-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(251 207 232 / var(--tw-ring-opacity, 1));
}
.ring-pink-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 168 212 / var(--tw-ring-opacity, 1));
}
.ring-pink-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(244 114 182 / var(--tw-ring-opacity, 1));
}
.ring-pink-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(253 242 248 / var(--tw-ring-opacity, 1));
}
.ring-pink-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(236 72 153 / var(--tw-ring-opacity, 1));
}
.ring-pink-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(219 39 119 / var(--tw-ring-opacity, 1));
}
.ring-pink-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(190 24 93 / var(--tw-ring-opacity, 1));
}
.ring-pink-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(157 23 77 / var(--tw-ring-opacity, 1));
}
.ring-pink-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(131 24 67 / var(--tw-ring-opacity, 1));
}
.ring-pink-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(80 7 36 / var(--tw-ring-opacity, 1));
}
.ring-purple-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(243 232 255 / var(--tw-ring-opacity, 1));
}
.ring-purple-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(233 213 255 / var(--tw-ring-opacity, 1));
}
.ring-purple-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(216 180 254 / var(--tw-ring-opacity, 1));
}
.ring-purple-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(192 132 252 / var(--tw-ring-opacity, 1));
}
.ring-purple-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 245 255 / var(--tw-ring-opacity, 1));
}
.ring-purple-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}
.ring-purple-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 51 234 / var(--tw-ring-opacity, 1));
}
.ring-purple-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(126 34 206 / var(--tw-ring-opacity, 1));
}
.ring-purple-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(107 33 168 / var(--tw-ring-opacity, 1));
}
.ring-purple-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(88 28 135 / var(--tw-ring-opacity, 1));
}
.ring-purple-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 7 100 / var(--tw-ring-opacity, 1));
}
.ring-red-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 226 226 / var(--tw-ring-opacity, 1));
}
.ring-red-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 202 202 / var(--tw-ring-opacity, 1));
}
.ring-red-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(252 165 165 / var(--tw-ring-opacity, 1));
}
.ring-red-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));
}
.ring-red-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 242 242 / var(--tw-ring-opacity, 1));
}
.ring-red-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}
.ring-red-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(220 38 38 / var(--tw-ring-opacity, 1));
}
.ring-red-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(185 28 28 / var(--tw-ring-opacity, 1));
}
.ring-red-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(153 27 27 / var(--tw-ring-opacity, 1));
}
.ring-red-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(127 29 29 / var(--tw-ring-opacity, 1));
}
.ring-red-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(69 10 10 / var(--tw-ring-opacity, 1));
}
.ring-rose-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 228 230 / var(--tw-ring-opacity, 1));
}
.ring-rose-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 205 211 / var(--tw-ring-opacity, 1));
}
.ring-rose-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(253 164 175 / var(--tw-ring-opacity, 1));
}
.ring-rose-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(251 113 133 / var(--tw-ring-opacity, 1));
}
.ring-rose-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 241 242 / var(--tw-ring-opacity, 1));
}
.ring-rose-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(244 63 94 / var(--tw-ring-opacity, 1));
}
.ring-rose-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(225 29 72 / var(--tw-ring-opacity, 1));
}
.ring-rose-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(190 18 60 / var(--tw-ring-opacity, 1));
}
.ring-rose-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(159 18 57 / var(--tw-ring-opacity, 1));
}
.ring-rose-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(136 19 55 / var(--tw-ring-opacity, 1));
}
.ring-rose-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(76 5 25 / var(--tw-ring-opacity, 1));
}
.ring-sky-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(224 242 254 / var(--tw-ring-opacity, 1));
}
.ring-sky-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(186 230 253 / var(--tw-ring-opacity, 1));
}
.ring-sky-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(125 211 252 / var(--tw-ring-opacity, 1));
}
.ring-sky-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(56 189 248 / var(--tw-ring-opacity, 1));
}
.ring-sky-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(240 249 255 / var(--tw-ring-opacity, 1));
}
.ring-sky-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));
}
.ring-sky-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(2 132 199 / var(--tw-ring-opacity, 1));
}
.ring-sky-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(3 105 161 / var(--tw-ring-opacity, 1));
}
.ring-sky-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(7 89 133 / var(--tw-ring-opacity, 1));
}
.ring-sky-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(12 74 110 / var(--tw-ring-opacity, 1));
}
.ring-sky-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(8 47 73 / var(--tw-ring-opacity, 1));
}
.ring-slate-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(241 245 249 / var(--tw-ring-opacity, 1));
}
.ring-slate-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(226 232 240 / var(--tw-ring-opacity, 1));
}
.ring-slate-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(203 213 225 / var(--tw-ring-opacity, 1));
}
.ring-slate-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(148 163 184 / var(--tw-ring-opacity, 1));
}
.ring-slate-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 250 252 / var(--tw-ring-opacity, 1));
}
.ring-slate-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(100 116 139 / var(--tw-ring-opacity, 1));
}
.ring-slate-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(71 85 105 / var(--tw-ring-opacity, 1));
}
.ring-slate-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(51 65 85 / var(--tw-ring-opacity, 1));
}
.ring-slate-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 41 59 / var(--tw-ring-opacity, 1));
}
.ring-slate-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(15 23 42 / var(--tw-ring-opacity, 1));
}
.ring-slate-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(2 6 23 / var(--tw-ring-opacity, 1));
}
.ring-stone-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(245 245 244 / var(--tw-ring-opacity, 1));
}
.ring-stone-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(231 229 228 / var(--tw-ring-opacity, 1));
}
.ring-stone-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(214 211 209 / var(--tw-ring-opacity, 1));
}
.ring-stone-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 162 158 / var(--tw-ring-opacity, 1));
}
.ring-stone-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 250 249 / var(--tw-ring-opacity, 1));
}
.ring-stone-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(120 113 108 / var(--tw-ring-opacity, 1));
}
.ring-stone-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(87 83 78 / var(--tw-ring-opacity, 1));
}
.ring-stone-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(68 64 60 / var(--tw-ring-opacity, 1));
}
.ring-stone-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(41 37 36 / var(--tw-ring-opacity, 1));
}
.ring-stone-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(28 25 23 / var(--tw-ring-opacity, 1));
}
.ring-stone-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(12 10 9 / var(--tw-ring-opacity, 1));
}
.ring-teal-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(204 251 241 / var(--tw-ring-opacity, 1));
}
.ring-teal-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(153 246 228 / var(--tw-ring-opacity, 1));
}
.ring-teal-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(94 234 212 / var(--tw-ring-opacity, 1));
}
.ring-teal-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(45 212 191 / var(--tw-ring-opacity, 1));
}
.ring-teal-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(240 253 250 / var(--tw-ring-opacity, 1));
}
.ring-teal-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(20 184 166 / var(--tw-ring-opacity, 1));
}
.ring-teal-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(13 148 136 / var(--tw-ring-opacity, 1));
}
.ring-teal-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(15 118 110 / var(--tw-ring-opacity, 1));
}
.ring-teal-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 94 89 / var(--tw-ring-opacity, 1));
}
.ring-teal-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(19 78 74 / var(--tw-ring-opacity, 1));
}
.ring-teal-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(4 47 46 / var(--tw-ring-opacity, 1));
}
.ring-tremor-brand-inverted {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}
.ring-tremor-brand-muted {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(191 219 254 / var(--tw-ring-opacity, 1));
}
.ring-tremor-brand\/20 {
  --tw-ring-color: rgb(59 130 246 / 0.2);
}
.ring-tremor-ring {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity, 1));
}
.ring-violet-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(237 233 254 / var(--tw-ring-opacity, 1));
}
.ring-violet-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(221 214 254 / var(--tw-ring-opacity, 1));
}
.ring-violet-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(196 181 253 / var(--tw-ring-opacity, 1));
}
.ring-violet-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(167 139 250 / var(--tw-ring-opacity, 1));
}
.ring-violet-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(245 243 255 / var(--tw-ring-opacity, 1));
}
.ring-violet-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(139 92 246 / var(--tw-ring-opacity, 1));
}
.ring-violet-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(124 58 237 / var(--tw-ring-opacity, 1));
}
.ring-violet-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(109 40 217 / var(--tw-ring-opacity, 1));
}
.ring-violet-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(91 33 182 / var(--tw-ring-opacity, 1));
}
.ring-violet-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(76 29 149 / var(--tw-ring-opacity, 1));
}
.ring-violet-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(46 16 101 / var(--tw-ring-opacity, 1));
}
.ring-yellow-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 249 195 / var(--tw-ring-opacity, 1));
}
.ring-yellow-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 240 138 / var(--tw-ring-opacity, 1));
}
.ring-yellow-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(253 224 71 / var(--tw-ring-opacity, 1));
}
.ring-yellow-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 204 21 / var(--tw-ring-opacity, 1));
}
.ring-yellow-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 252 232 / var(--tw-ring-opacity, 1));
}
.ring-yellow-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity, 1));
}
.ring-yellow-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(202 138 4 / var(--tw-ring-opacity, 1));
}
.ring-yellow-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(161 98 7 / var(--tw-ring-opacity, 1));
}
.ring-yellow-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(133 77 14 / var(--tw-ring-opacity, 1));
}
.ring-yellow-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(113 63 18 / var(--tw-ring-opacity, 1));
}
.ring-yellow-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(66 32 6 / var(--tw-ring-opacity, 1));
}
.ring-zinc-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(244 244 245 / var(--tw-ring-opacity, 1));
}
.ring-zinc-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(228 228 231 / var(--tw-ring-opacity, 1));
}
.ring-zinc-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(212 212 216 / var(--tw-ring-opacity, 1));
}
.ring-zinc-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(161 161 170 / var(--tw-ring-opacity, 1));
}
.ring-zinc-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 250 250 / var(--tw-ring-opacity, 1));
}
.ring-zinc-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(113 113 122 / var(--tw-ring-opacity, 1));
}
.ring-zinc-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(82 82 91 / var(--tw-ring-opacity, 1));
}
.ring-zinc-700 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(63 63 70 / var(--tw-ring-opacity, 1));
}
.ring-zinc-800 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(39 39 42 / var(--tw-ring-opacity, 1));
}
.ring-zinc-900 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(24 24 27 / var(--tw-ring-opacity, 1));
}
.ring-zinc-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(9 9 11 / var(--tw-ring-opacity, 1));
}
.ring-opacity-20 {
  --tw-ring-opacity: 0.2;
}
.ring-opacity-40 {
  --tw-ring-opacity: 0.4;
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-100 {
  transition-duration: 100ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.duration-75 {
  transition-duration: 75ms;
}
.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear {
  transition-timing-function: linear;
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.\[--anchor-gap\:4px\] {
  --anchor-gap: 4px;
}
.\[appearance\:textfield\] {
  -webkit-appearance: textfield;
     -moz-appearance: textfield;
          appearance: textfield;
}
.\[scrollbar-width\:none\] {
  scrollbar-width: none;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

.placeholder\:text-red-500::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.placeholder\:text-red-500::placeholder {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.placeholder\:text-tremor-content::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.placeholder\:text-tremor-content::placeholder {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.placeholder\:text-tremor-content-subtle::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.placeholder\:text-tremor-content-subtle::placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.first\:rounded-l-\[4px\]:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.last\:rounded-r-\[4px\]:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.focus-within\:relative:focus-within {
  position: relative;
}

.hover\:border-b-2:hover {
  border-bottom-width: 2px;
}

.hover\:border-amber-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(254 243 199 / var(--tw-border-opacity, 1));
}

.hover\:border-amber-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));
}

.hover\:border-amber-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(252 211 77 / var(--tw-border-opacity, 1));
}

.hover\:border-amber-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(251 191 36 / var(--tw-border-opacity, 1));
}

.hover\:border-amber-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 251 235 / var(--tw-border-opacity, 1));
}

.hover\:border-amber-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}

.hover\:border-amber-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(217 119 6 / var(--tw-border-opacity, 1));
}

.hover\:border-amber-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(180 83 9 / var(--tw-border-opacity, 1));
}

.hover\:border-amber-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(146 64 14 / var(--tw-border-opacity, 1));
}

.hover\:border-amber-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(120 53 15 / var(--tw-border-opacity, 1));
}

.hover\:border-amber-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(69 26 3 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(219 234 254 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(239 246 255 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(30 58 138 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(23 37 84 / var(--tw-border-opacity, 1));
}

.hover\:border-cyan-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(207 250 254 / var(--tw-border-opacity, 1));
}

.hover\:border-cyan-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(165 243 252 / var(--tw-border-opacity, 1));
}

.hover\:border-cyan-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(103 232 249 / var(--tw-border-opacity, 1));
}

.hover\:border-cyan-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(34 211 238 / var(--tw-border-opacity, 1));
}

.hover\:border-cyan-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(236 254 255 / var(--tw-border-opacity, 1));
}

.hover\:border-cyan-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(6 182 212 / var(--tw-border-opacity, 1));
}

.hover\:border-cyan-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(8 145 178 / var(--tw-border-opacity, 1));
}

.hover\:border-cyan-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(14 116 144 / var(--tw-border-opacity, 1));
}

.hover\:border-cyan-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(21 94 117 / var(--tw-border-opacity, 1));
}

.hover\:border-cyan-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(22 78 99 / var(--tw-border-opacity, 1));
}

.hover\:border-cyan-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(8 51 68 / var(--tw-border-opacity, 1));
}

.hover\:border-emerald-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 250 229 / var(--tw-border-opacity, 1));
}

.hover\:border-emerald-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(167 243 208 / var(--tw-border-opacity, 1));
}

.hover\:border-emerald-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(110 231 183 / var(--tw-border-opacity, 1));
}

.hover\:border-emerald-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(52 211 153 / var(--tw-border-opacity, 1));
}

.hover\:border-emerald-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(236 253 245 / var(--tw-border-opacity, 1));
}

.hover\:border-emerald-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));
}

.hover\:border-emerald-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(5 150 105 / var(--tw-border-opacity, 1));
}

.hover\:border-emerald-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(4 120 87 / var(--tw-border-opacity, 1));
}

.hover\:border-emerald-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(6 95 70 / var(--tw-border-opacity, 1));
}

.hover\:border-emerald-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(6 78 59 / var(--tw-border-opacity, 1));
}

.hover\:border-emerald-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(2 44 34 / var(--tw-border-opacity, 1));
}

.hover\:border-fuchsia-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(250 232 255 / var(--tw-border-opacity, 1));
}

.hover\:border-fuchsia-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(245 208 254 / var(--tw-border-opacity, 1));
}

.hover\:border-fuchsia-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(240 171 252 / var(--tw-border-opacity, 1));
}

.hover\:border-fuchsia-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(232 121 249 / var(--tw-border-opacity, 1));
}

.hover\:border-fuchsia-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(253 244 255 / var(--tw-border-opacity, 1));
}

.hover\:border-fuchsia-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(217 70 239 / var(--tw-border-opacity, 1));
}

.hover\:border-fuchsia-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(192 38 211 / var(--tw-border-opacity, 1));
}

.hover\:border-fuchsia-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(162 28 175 / var(--tw-border-opacity, 1));
}

.hover\:border-fuchsia-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(134 25 143 / var(--tw-border-opacity, 1));
}

.hover\:border-fuchsia-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(112 26 117 / var(--tw-border-opacity, 1));
}

.hover\:border-fuchsia-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(74 4 78 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(249 250 251 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(3 7 18 / var(--tw-border-opacity, 1));
}

.hover\:border-green-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(220 252 231 / var(--tw-border-opacity, 1));
}

.hover\:border-green-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}

.hover\:border-green-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}

.hover\:border-green-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}

.hover\:border-green-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(240 253 244 / var(--tw-border-opacity, 1));
}

.hover\:border-green-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.hover\:border-green-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}

.hover\:border-green-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
}

.hover\:border-green-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(22 101 52 / var(--tw-border-opacity, 1));
}

.hover\:border-green-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(20 83 45 / var(--tw-border-opacity, 1));
}

.hover\:border-green-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(5 46 22 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(224 231 255 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(199 210 254 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(165 180 252 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(129 140 248 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(238 242 255 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(67 56 202 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(55 48 163 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(49 46 129 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(30 27 75 / var(--tw-border-opacity, 1));
}

.hover\:border-lime-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(236 252 203 / var(--tw-border-opacity, 1));
}

.hover\:border-lime-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(217 249 157 / var(--tw-border-opacity, 1));
}

.hover\:border-lime-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(190 242 100 / var(--tw-border-opacity, 1));
}

.hover\:border-lime-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(163 230 53 / var(--tw-border-opacity, 1));
}

.hover\:border-lime-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(247 254 231 / var(--tw-border-opacity, 1));
}

.hover\:border-lime-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(132 204 22 / var(--tw-border-opacity, 1));
}

.hover\:border-lime-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(101 163 13 / var(--tw-border-opacity, 1));
}

.hover\:border-lime-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(77 124 15 / var(--tw-border-opacity, 1));
}

.hover\:border-lime-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(63 98 18 / var(--tw-border-opacity, 1));
}

.hover\:border-lime-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(54 83 20 / var(--tw-border-opacity, 1));
}

.hover\:border-lime-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(26 46 5 / var(--tw-border-opacity, 1));
}

.hover\:border-neutral-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));
}

.hover\:border-neutral-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));
}

.hover\:border-neutral-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
}

.hover\:border-neutral-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(163 163 163 / var(--tw-border-opacity, 1));
}

.hover\:border-neutral-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(250 250 250 / var(--tw-border-opacity, 1));
}

.hover\:border-neutral-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(115 115 115 / var(--tw-border-opacity, 1));
}

.hover\:border-neutral-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(82 82 82 / var(--tw-border-opacity, 1));
}

.hover\:border-neutral-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(64 64 64 / var(--tw-border-opacity, 1));
}

.hover\:border-neutral-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(38 38 38 / var(--tw-border-opacity, 1));
}

.hover\:border-neutral-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(23 23 23 / var(--tw-border-opacity, 1));
}

.hover\:border-neutral-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(10 10 10 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 237 213 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(253 186 116 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 247 237 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(234 88 12 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(194 65 12 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(154 52 18 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(124 45 18 / var(--tw-border-opacity, 1));
}

.hover\:border-orange-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(67 20 7 / var(--tw-border-opacity, 1));
}

.hover\:border-pink-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(252 231 243 / var(--tw-border-opacity, 1));
}

.hover\:border-pink-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(251 207 232 / var(--tw-border-opacity, 1));
}

.hover\:border-pink-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(249 168 212 / var(--tw-border-opacity, 1));
}

.hover\:border-pink-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(244 114 182 / var(--tw-border-opacity, 1));
}

.hover\:border-pink-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(253 242 248 / var(--tw-border-opacity, 1));
}

.hover\:border-pink-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(236 72 153 / var(--tw-border-opacity, 1));
}

.hover\:border-pink-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(219 39 119 / var(--tw-border-opacity, 1));
}

.hover\:border-pink-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(190 24 93 / var(--tw-border-opacity, 1));
}

.hover\:border-pink-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(157 23 77 / var(--tw-border-opacity, 1));
}

.hover\:border-pink-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(131 24 67 / var(--tw-border-opacity, 1));
}

.hover\:border-pink-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(80 7 36 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(243 232 255 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(250 245 255 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(126 34 206 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(107 33 168 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(88 28 135 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(59 7 100 / var(--tw-border-opacity, 1));
}

.hover\:border-red-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(254 226 226 / var(--tw-border-opacity, 1));
}

.hover\:border-red-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}

.hover\:border-red-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}

.hover\:border-red-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}

.hover\:border-red-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(254 242 242 / var(--tw-border-opacity, 1));
}

.hover\:border-red-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.hover\:border-red-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}

.hover\:border-red-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
}

.hover\:border-red-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));
}

.hover\:border-red-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(127 29 29 / var(--tw-border-opacity, 1));
}

.hover\:border-red-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(69 10 10 / var(--tw-border-opacity, 1));
}

.hover\:border-rose-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 228 230 / var(--tw-border-opacity, 1));
}

.hover\:border-rose-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(254 205 211 / var(--tw-border-opacity, 1));
}

.hover\:border-rose-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(253 164 175 / var(--tw-border-opacity, 1));
}

.hover\:border-rose-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(251 113 133 / var(--tw-border-opacity, 1));
}

.hover\:border-rose-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 241 242 / var(--tw-border-opacity, 1));
}

.hover\:border-rose-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(244 63 94 / var(--tw-border-opacity, 1));
}

.hover\:border-rose-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(225 29 72 / var(--tw-border-opacity, 1));
}

.hover\:border-rose-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(190 18 60 / var(--tw-border-opacity, 1));
}

.hover\:border-rose-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(159 18 57 / var(--tw-border-opacity, 1));
}

.hover\:border-rose-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(136 19 55 / var(--tw-border-opacity, 1));
}

.hover\:border-rose-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(76 5 25 / var(--tw-border-opacity, 1));
}

.hover\:border-sky-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(224 242 254 / var(--tw-border-opacity, 1));
}

.hover\:border-sky-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(186 230 253 / var(--tw-border-opacity, 1));
}

.hover\:border-sky-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(125 211 252 / var(--tw-border-opacity, 1));
}

.hover\:border-sky-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(56 189 248 / var(--tw-border-opacity, 1));
}

.hover\:border-sky-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(240 249 255 / var(--tw-border-opacity, 1));
}

.hover\:border-sky-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));
}

.hover\:border-sky-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(2 132 199 / var(--tw-border-opacity, 1));
}

.hover\:border-sky-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(3 105 161 / var(--tw-border-opacity, 1));
}

.hover\:border-sky-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(7 89 133 / var(--tw-border-opacity, 1));
}

.hover\:border-sky-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(12 74 110 / var(--tw-border-opacity, 1));
}

.hover\:border-sky-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(8 47 73 / var(--tw-border-opacity, 1));
}

.hover\:border-slate-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity, 1));
}

.hover\:border-slate-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
}

.hover\:border-slate-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}

.hover\:border-slate-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(148 163 184 / var(--tw-border-opacity, 1));
}

.hover\:border-slate-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(248 250 252 / var(--tw-border-opacity, 1));
}

.hover\:border-slate-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}

.hover\:border-slate-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));
}

.hover\:border-slate-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));
}

.hover\:border-slate-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));
}

.hover\:border-slate-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(15 23 42 / var(--tw-border-opacity, 1));
}

.hover\:border-slate-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(2 6 23 / var(--tw-border-opacity, 1));
}

.hover\:border-stone-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(245 245 244 / var(--tw-border-opacity, 1));
}

.hover\:border-stone-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(231 229 228 / var(--tw-border-opacity, 1));
}

.hover\:border-stone-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(214 211 209 / var(--tw-border-opacity, 1));
}

.hover\:border-stone-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(168 162 158 / var(--tw-border-opacity, 1));
}

.hover\:border-stone-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(250 250 249 / var(--tw-border-opacity, 1));
}

.hover\:border-stone-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(120 113 108 / var(--tw-border-opacity, 1));
}

.hover\:border-stone-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(87 83 78 / var(--tw-border-opacity, 1));
}

.hover\:border-stone-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(68 64 60 / var(--tw-border-opacity, 1));
}

.hover\:border-stone-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(41 37 36 / var(--tw-border-opacity, 1));
}

.hover\:border-stone-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(28 25 23 / var(--tw-border-opacity, 1));
}

.hover\:border-stone-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(12 10 9 / var(--tw-border-opacity, 1));
}

.hover\:border-teal-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(204 251 241 / var(--tw-border-opacity, 1));
}

.hover\:border-teal-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(153 246 228 / var(--tw-border-opacity, 1));
}

.hover\:border-teal-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(94 234 212 / var(--tw-border-opacity, 1));
}

.hover\:border-teal-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(45 212 191 / var(--tw-border-opacity, 1));
}

.hover\:border-teal-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(240 253 250 / var(--tw-border-opacity, 1));
}

.hover\:border-teal-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(20 184 166 / var(--tw-border-opacity, 1));
}

.hover\:border-teal-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(13 148 136 / var(--tw-border-opacity, 1));
}

.hover\:border-teal-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(15 118 110 / var(--tw-border-opacity, 1));
}

.hover\:border-teal-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(17 94 89 / var(--tw-border-opacity, 1));
}

.hover\:border-teal-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(19 78 74 / var(--tw-border-opacity, 1));
}

.hover\:border-teal-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(4 47 46 / var(--tw-border-opacity, 1));
}

.hover\:border-tremor-brand-emphasis:hover {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}

.hover\:border-tremor-content:hover {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}

.hover\:border-violet-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(237 233 254 / var(--tw-border-opacity, 1));
}

.hover\:border-violet-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(221 214 254 / var(--tw-border-opacity, 1));
}

.hover\:border-violet-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(196 181 253 / var(--tw-border-opacity, 1));
}

.hover\:border-violet-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(167 139 250 / var(--tw-border-opacity, 1));
}

.hover\:border-violet-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(245 243 255 / var(--tw-border-opacity, 1));
}

.hover\:border-violet-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(139 92 246 / var(--tw-border-opacity, 1));
}

.hover\:border-violet-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(124 58 237 / var(--tw-border-opacity, 1));
}

.hover\:border-violet-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(109 40 217 / var(--tw-border-opacity, 1));
}

.hover\:border-violet-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(91 33 182 / var(--tw-border-opacity, 1));
}

.hover\:border-violet-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(76 29 149 / var(--tw-border-opacity, 1));
}

.hover\:border-violet-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(46 16 101 / var(--tw-border-opacity, 1));
}

.hover\:border-yellow-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(254 249 195 / var(--tw-border-opacity, 1));
}

.hover\:border-yellow-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}

.hover\:border-yellow-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(253 224 71 / var(--tw-border-opacity, 1));
}

.hover\:border-yellow-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(250 204 21 / var(--tw-border-opacity, 1));
}

.hover\:border-yellow-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(254 252 232 / var(--tw-border-opacity, 1));
}

.hover\:border-yellow-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));
}

.hover\:border-yellow-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(202 138 4 / var(--tw-border-opacity, 1));
}

.hover\:border-yellow-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(161 98 7 / var(--tw-border-opacity, 1));
}

.hover\:border-yellow-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(133 77 14 / var(--tw-border-opacity, 1));
}

.hover\:border-yellow-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(113 63 18 / var(--tw-border-opacity, 1));
}

.hover\:border-yellow-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(66 32 6 / var(--tw-border-opacity, 1));
}

.hover\:border-zinc-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(244 244 245 / var(--tw-border-opacity, 1));
}

.hover\:border-zinc-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(228 228 231 / var(--tw-border-opacity, 1));
}

.hover\:border-zinc-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(212 212 216 / var(--tw-border-opacity, 1));
}

.hover\:border-zinc-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(161 161 170 / var(--tw-border-opacity, 1));
}

.hover\:border-zinc-50:hover {
  --tw-border-opacity: 1;
  border-color: rgb(250 250 250 / var(--tw-border-opacity, 1));
}

.hover\:border-zinc-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(113 113 122 / var(--tw-border-opacity, 1));
}

.hover\:border-zinc-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));
}

.hover\:border-zinc-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(63 63 70 / var(--tw-border-opacity, 1));
}

.hover\:border-zinc-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(39 39 42 / var(--tw-border-opacity, 1));
}

.hover\:border-zinc-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(24 24 27 / var(--tw-border-opacity, 1));
}

.hover\:border-zinc-950:hover {
  --tw-border-opacity: 1;
  border-color: rgb(9 9 11 / var(--tw-border-opacity, 1));
}

.hover\:bg-amber-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));
}

.hover\:bg-amber-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(253 230 138 / var(--tw-bg-opacity, 1));
}

.hover\:bg-amber-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(252 211 77 / var(--tw-bg-opacity, 1));
}

.hover\:bg-amber-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(251 191 36 / var(--tw-bg-opacity, 1));
}

.hover\:bg-amber-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-amber-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}

.hover\:bg-amber-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1));
}

.hover\:bg-amber-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(180 83 9 / var(--tw-bg-opacity, 1));
}

.hover\:bg-amber-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(146 64 14 / var(--tw-bg-opacity, 1));
}

.hover\:bg-amber-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(120 53 15 / var(--tw-bg-opacity, 1));
}

.hover\:bg-amber-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(69 26 3 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(23 37 84 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cyan-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cyan-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(165 243 252 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cyan-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(103 232 249 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cyan-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(34 211 238 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cyan-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cyan-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cyan-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(8 145 178 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cyan-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(14 116 144 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cyan-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 94 117 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cyan-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 78 99 / var(--tw-bg-opacity, 1));
}

.hover\:bg-cyan-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(8 51 68 / var(--tw-bg-opacity, 1));
}

.hover\:bg-emerald-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));
}

.hover\:bg-emerald-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(167 243 208 / var(--tw-bg-opacity, 1));
}

.hover\:bg-emerald-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(110 231 183 / var(--tw-bg-opacity, 1));
}

.hover\:bg-emerald-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(52 211 153 / var(--tw-bg-opacity, 1));
}

.hover\:bg-emerald-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));
}

.hover\:bg-emerald-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}

.hover\:bg-emerald-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(5 150 105 / var(--tw-bg-opacity, 1));
}

.hover\:bg-emerald-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(4 120 87 / var(--tw-bg-opacity, 1));
}

.hover\:bg-emerald-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(6 95 70 / var(--tw-bg-opacity, 1));
}

.hover\:bg-emerald-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(6 78 59 / var(--tw-bg-opacity, 1));
}

.hover\:bg-emerald-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(2 44 34 / var(--tw-bg-opacity, 1));
}

.hover\:bg-fuchsia-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 232 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-fuchsia-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 208 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-fuchsia-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 171 252 / var(--tw-bg-opacity, 1));
}

.hover\:bg-fuchsia-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(232 121 249 / var(--tw-bg-opacity, 1));
}

.hover\:bg-fuchsia-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(253 244 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-fuchsia-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(217 70 239 / var(--tw-bg-opacity, 1));
}

.hover\:bg-fuchsia-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(192 38 211 / var(--tw-bg-opacity, 1));
}

.hover\:bg-fuchsia-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(162 28 175 / var(--tw-bg-opacity, 1));
}

.hover\:bg-fuchsia-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(134 25 143 / var(--tw-bg-opacity, 1));
}

.hover\:bg-fuchsia-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(112 26 117 / var(--tw-bg-opacity, 1));
}

.hover\:bg-fuchsia-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(74 4 78 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(3 7 18 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(134 239 172 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 101 52 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(5 46 22 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(199 210 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(165 180 252 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(129 140 248 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 48 163 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(49 46 129 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(30 27 75 / var(--tw-bg-opacity, 1));
}

.hover\:bg-lime-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(236 252 203 / var(--tw-bg-opacity, 1));
}

.hover\:bg-lime-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(217 249 157 / var(--tw-bg-opacity, 1));
}

.hover\:bg-lime-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(190 242 100 / var(--tw-bg-opacity, 1));
}

.hover\:bg-lime-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(163 230 53 / var(--tw-bg-opacity, 1));
}

.hover\:bg-lime-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(247 254 231 / var(--tw-bg-opacity, 1));
}

.hover\:bg-lime-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(132 204 22 / var(--tw-bg-opacity, 1));
}

.hover\:bg-lime-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(101 163 13 / var(--tw-bg-opacity, 1));
}

.hover\:bg-lime-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(77 124 15 / var(--tw-bg-opacity, 1));
}

.hover\:bg-lime-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(63 98 18 / var(--tw-bg-opacity, 1));
}

.hover\:bg-lime-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(54 83 20 / var(--tw-bg-opacity, 1));
}

.hover\:bg-lime-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(26 46 5 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 212 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(163 163 163 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(115 115 115 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 82 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(64 64 64 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(38 38 38 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(23 23 23 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(10 10 10 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 215 170 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(253 186 116 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(251 146 60 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(194 65 12 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(154 52 18 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(124 45 18 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(67 20 7 / var(--tw-bg-opacity, 1));
}

.hover\:bg-pink-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(252 231 243 / var(--tw-bg-opacity, 1));
}

.hover\:bg-pink-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(251 207 232 / var(--tw-bg-opacity, 1));
}

.hover\:bg-pink-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 168 212 / var(--tw-bg-opacity, 1));
}

.hover\:bg-pink-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(244 114 182 / var(--tw-bg-opacity, 1));
}

.hover\:bg-pink-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(253 242 248 / var(--tw-bg-opacity, 1));
}

.hover\:bg-pink-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));
}

.hover\:bg-pink-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(219 39 119 / var(--tw-bg-opacity, 1));
}

.hover\:bg-pink-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(190 24 93 / var(--tw-bg-opacity, 1));
}

.hover\:bg-pink-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(157 23 77 / var(--tw-bg-opacity, 1));
}

.hover\:bg-pink-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(131 24 67 / var(--tw-bg-opacity, 1));
}

.hover\:bg-pink-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(80 7 36 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(216 180 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(107 33 168 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(59 7 100 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(252 165 165 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(69 10 10 / var(--tw-bg-opacity, 1));
}

.hover\:bg-rose-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 228 230 / var(--tw-bg-opacity, 1));
}

.hover\:bg-rose-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 205 211 / var(--tw-bg-opacity, 1));
}

.hover\:bg-rose-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(253 164 175 / var(--tw-bg-opacity, 1));
}

.hover\:bg-rose-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(251 113 133 / var(--tw-bg-opacity, 1));
}

.hover\:bg-rose-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 241 242 / var(--tw-bg-opacity, 1));
}

.hover\:bg-rose-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(244 63 94 / var(--tw-bg-opacity, 1));
}

.hover\:bg-rose-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(225 29 72 / var(--tw-bg-opacity, 1));
}

.hover\:bg-rose-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(190 18 60 / var(--tw-bg-opacity, 1));
}

.hover\:bg-rose-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(159 18 57 / var(--tw-bg-opacity, 1));
}

.hover\:bg-rose-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(136 19 55 / var(--tw-bg-opacity, 1));
}

.hover\:bg-rose-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(76 5 25 / var(--tw-bg-opacity, 1));
}

.hover\:bg-sky-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-sky-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(186 230 253 / var(--tw-bg-opacity, 1));
}

.hover\:bg-sky-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(125 211 252 / var(--tw-bg-opacity, 1));
}

.hover\:bg-sky-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(56 189 248 / var(--tw-bg-opacity, 1));
}

.hover\:bg-sky-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-sky-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity, 1));
}

.hover\:bg-sky-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));
}

.hover\:bg-sky-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(3 105 161 / var(--tw-bg-opacity, 1));
}

.hover\:bg-sky-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(7 89 133 / var(--tw-bg-opacity, 1));
}

.hover\:bg-sky-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(12 74 110 / var(--tw-bg-opacity, 1));
}

.hover\:bg-sky-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(8 47 73 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(203 213 225 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(148 163 184 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
}

.hover\:bg-slate-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(2 6 23 / var(--tw-bg-opacity, 1));
}

.hover\:bg-stone-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 244 / var(--tw-bg-opacity, 1));
}

.hover\:bg-stone-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(231 229 228 / var(--tw-bg-opacity, 1));
}

.hover\:bg-stone-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(214 211 209 / var(--tw-bg-opacity, 1));
}

.hover\:bg-stone-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(168 162 158 / var(--tw-bg-opacity, 1));
}

.hover\:bg-stone-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 249 / var(--tw-bg-opacity, 1));
}

.hover\:bg-stone-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(120 113 108 / var(--tw-bg-opacity, 1));
}

.hover\:bg-stone-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(87 83 78 / var(--tw-bg-opacity, 1));
}

.hover\:bg-stone-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(68 64 60 / var(--tw-bg-opacity, 1));
}

.hover\:bg-stone-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(41 37 36 / var(--tw-bg-opacity, 1));
}

.hover\:bg-stone-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(28 25 23 / var(--tw-bg-opacity, 1));
}

.hover\:bg-stone-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(12 10 9 / var(--tw-bg-opacity, 1));
}

.hover\:bg-teal-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));
}

.hover\:bg-teal-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(153 246 228 / var(--tw-bg-opacity, 1));
}

.hover\:bg-teal-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(94 234 212 / var(--tw-bg-opacity, 1));
}

.hover\:bg-teal-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(45 212 191 / var(--tw-bg-opacity, 1));
}

.hover\:bg-teal-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 250 / var(--tw-bg-opacity, 1));
}

.hover\:bg-teal-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity, 1));
}

.hover\:bg-teal-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(13 148 136 / var(--tw-bg-opacity, 1));
}

.hover\:bg-teal-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(15 118 110 / var(--tw-bg-opacity, 1));
}

.hover\:bg-teal-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(17 94 89 / var(--tw-bg-opacity, 1));
}

.hover\:bg-teal-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(19 78 74 / var(--tw-bg-opacity, 1));
}

.hover\:bg-teal-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(4 47 46 / var(--tw-bg-opacity, 1));
}

.hover\:bg-tremor-background-muted:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-tremor-background-subtle:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-tremor-brand-emphasis:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-tremor-brand-faint:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-violet-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(237 233 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-violet-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(221 214 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-violet-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(196 181 253 / var(--tw-bg-opacity, 1));
}

.hover\:bg-violet-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(167 139 250 / var(--tw-bg-opacity, 1));
}

.hover\:bg-violet-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 243 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-violet-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(139 92 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-violet-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(124 58 237 / var(--tw-bg-opacity, 1));
}

.hover\:bg-violet-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(109 40 217 / var(--tw-bg-opacity, 1));
}

.hover\:bg-violet-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(91 33 182 / var(--tw-bg-opacity, 1));
}

.hover\:bg-violet-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(76 29 149 / var(--tw-bg-opacity, 1));
}

.hover\:bg-violet-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(46 16 101 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(253 224 71 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(133 77 14 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(113 63 18 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(66 32 6 / var(--tw-bg-opacity, 1));
}

.hover\:bg-zinc-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 245 / var(--tw-bg-opacity, 1));
}

.hover\:bg-zinc-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(228 228 231 / var(--tw-bg-opacity, 1));
}

.hover\:bg-zinc-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-zinc-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(161 161 170 / var(--tw-bg-opacity, 1));
}

.hover\:bg-zinc-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}

.hover\:bg-zinc-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(113 113 122 / var(--tw-bg-opacity, 1));
}

.hover\:bg-zinc-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));
}

.hover\:bg-zinc-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
}

.hover\:bg-zinc-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
}

.hover\:bg-zinc-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(24 24 27 / var(--tw-bg-opacity, 1));
}

.hover\:bg-zinc-950:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(9 9 11 / var(--tw-bg-opacity, 1));
}

.hover\:bg-opacity-20:hover {
  --tw-bg-opacity: 0.2;
}

.hover\:text-amber-100:hover {
  --tw-text-opacity: 1;
  color: rgb(254 243 199 / var(--tw-text-opacity, 1));
}

.hover\:text-amber-200:hover {
  --tw-text-opacity: 1;
  color: rgb(253 230 138 / var(--tw-text-opacity, 1));
}

.hover\:text-amber-300:hover {
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}

.hover\:text-amber-400:hover {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}

.hover\:text-amber-50:hover {
  --tw-text-opacity: 1;
  color: rgb(255 251 235 / var(--tw-text-opacity, 1));
}

.hover\:text-amber-500:hover {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}

.hover\:text-amber-600:hover {
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}

.hover\:text-amber-700:hover {
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity, 1));
}

.hover\:text-amber-800:hover {
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}

.hover\:text-amber-900:hover {
  --tw-text-opacity: 1;
  color: rgb(120 53 15 / var(--tw-text-opacity, 1));
}

.hover\:text-amber-950:hover {
  --tw-text-opacity: 1;
  color: rgb(69 26 3 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-100:hover {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-200:hover {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-300:hover {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-400:hover {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-50:hover {
  --tw-text-opacity: 1;
  color: rgb(239 246 255 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-500:hover {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-700:hover {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-900:hover {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-950:hover {
  --tw-text-opacity: 1;
  color: rgb(23 37 84 / var(--tw-text-opacity, 1));
}

.hover\:text-cyan-100:hover {
  --tw-text-opacity: 1;
  color: rgb(207 250 254 / var(--tw-text-opacity, 1));
}

.hover\:text-cyan-200:hover {
  --tw-text-opacity: 1;
  color: rgb(165 243 252 / var(--tw-text-opacity, 1));
}

.hover\:text-cyan-300:hover {
  --tw-text-opacity: 1;
  color: rgb(103 232 249 / var(--tw-text-opacity, 1));
}

.hover\:text-cyan-400:hover {
  --tw-text-opacity: 1;
  color: rgb(34 211 238 / var(--tw-text-opacity, 1));
}

.hover\:text-cyan-50:hover {
  --tw-text-opacity: 1;
  color: rgb(236 254 255 / var(--tw-text-opacity, 1));
}

.hover\:text-cyan-500:hover {
  --tw-text-opacity: 1;
  color: rgb(6 182 212 / var(--tw-text-opacity, 1));
}

.hover\:text-cyan-600:hover {
  --tw-text-opacity: 1;
  color: rgb(8 145 178 / var(--tw-text-opacity, 1));
}

.hover\:text-cyan-700:hover {
  --tw-text-opacity: 1;
  color: rgb(14 116 144 / var(--tw-text-opacity, 1));
}

.hover\:text-cyan-800:hover {
  --tw-text-opacity: 1;
  color: rgb(21 94 117 / var(--tw-text-opacity, 1));
}

.hover\:text-cyan-900:hover {
  --tw-text-opacity: 1;
  color: rgb(22 78 99 / var(--tw-text-opacity, 1));
}

.hover\:text-cyan-950:hover {
  --tw-text-opacity: 1;
  color: rgb(8 51 68 / var(--tw-text-opacity, 1));
}

.hover\:text-emerald-100:hover {
  --tw-text-opacity: 1;
  color: rgb(209 250 229 / var(--tw-text-opacity, 1));
}

.hover\:text-emerald-200:hover {
  --tw-text-opacity: 1;
  color: rgb(167 243 208 / var(--tw-text-opacity, 1));
}

.hover\:text-emerald-300:hover {
  --tw-text-opacity: 1;
  color: rgb(110 231 183 / var(--tw-text-opacity, 1));
}

.hover\:text-emerald-400:hover {
  --tw-text-opacity: 1;
  color: rgb(52 211 153 / var(--tw-text-opacity, 1));
}

.hover\:text-emerald-50:hover {
  --tw-text-opacity: 1;
  color: rgb(236 253 245 / var(--tw-text-opacity, 1));
}

.hover\:text-emerald-500:hover {
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}

.hover\:text-emerald-600:hover {
  --tw-text-opacity: 1;
  color: rgb(5 150 105 / var(--tw-text-opacity, 1));
}

.hover\:text-emerald-700:hover {
  --tw-text-opacity: 1;
  color: rgb(4 120 87 / var(--tw-text-opacity, 1));
}

.hover\:text-emerald-800:hover {
  --tw-text-opacity: 1;
  color: rgb(6 95 70 / var(--tw-text-opacity, 1));
}

.hover\:text-emerald-900:hover {
  --tw-text-opacity: 1;
  color: rgb(6 78 59 / var(--tw-text-opacity, 1));
}

.hover\:text-emerald-950:hover {
  --tw-text-opacity: 1;
  color: rgb(2 44 34 / var(--tw-text-opacity, 1));
}

.hover\:text-fuchsia-100:hover {
  --tw-text-opacity: 1;
  color: rgb(250 232 255 / var(--tw-text-opacity, 1));
}

.hover\:text-fuchsia-200:hover {
  --tw-text-opacity: 1;
  color: rgb(245 208 254 / var(--tw-text-opacity, 1));
}

.hover\:text-fuchsia-300:hover {
  --tw-text-opacity: 1;
  color: rgb(240 171 252 / var(--tw-text-opacity, 1));
}

.hover\:text-fuchsia-400:hover {
  --tw-text-opacity: 1;
  color: rgb(232 121 249 / var(--tw-text-opacity, 1));
}

.hover\:text-fuchsia-50:hover {
  --tw-text-opacity: 1;
  color: rgb(253 244 255 / var(--tw-text-opacity, 1));
}

.hover\:text-fuchsia-500:hover {
  --tw-text-opacity: 1;
  color: rgb(217 70 239 / var(--tw-text-opacity, 1));
}

.hover\:text-fuchsia-600:hover {
  --tw-text-opacity: 1;
  color: rgb(192 38 211 / var(--tw-text-opacity, 1));
}

.hover\:text-fuchsia-700:hover {
  --tw-text-opacity: 1;
  color: rgb(162 28 175 / var(--tw-text-opacity, 1));
}

.hover\:text-fuchsia-800:hover {
  --tw-text-opacity: 1;
  color: rgb(134 25 143 / var(--tw-text-opacity, 1));
}

.hover\:text-fuchsia-900:hover {
  --tw-text-opacity: 1;
  color: rgb(112 26 117 / var(--tw-text-opacity, 1));
}

.hover\:text-fuchsia-950:hover {
  --tw-text-opacity: 1;
  color: rgb(74 4 78 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-100:hover {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-200:hover {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-300:hover {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-400:hover {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-50:hover {
  --tw-text-opacity: 1;
  color: rgb(249 250 251 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-500:hover {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-950:hover {
  --tw-text-opacity: 1;
  color: rgb(3 7 18 / var(--tw-text-opacity, 1));
}

.hover\:text-green-100:hover {
  --tw-text-opacity: 1;
  color: rgb(220 252 231 / var(--tw-text-opacity, 1));
}

.hover\:text-green-200:hover {
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}

.hover\:text-green-300:hover {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}

.hover\:text-green-400:hover {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.hover\:text-green-50:hover {
  --tw-text-opacity: 1;
  color: rgb(240 253 244 / var(--tw-text-opacity, 1));
}

.hover\:text-green-500:hover {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.hover\:text-green-600:hover {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.hover\:text-green-700:hover {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.hover\:text-green-800:hover {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.hover\:text-green-900:hover {
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}

.hover\:text-green-950:hover {
  --tw-text-opacity: 1;
  color: rgb(5 46 22 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-100:hover {
  --tw-text-opacity: 1;
  color: rgb(224 231 255 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-200:hover {
  --tw-text-opacity: 1;
  color: rgb(199 210 254 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-300:hover {
  --tw-text-opacity: 1;
  color: rgb(165 180 252 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-400:hover {
  --tw-text-opacity: 1;
  color: rgb(129 140 248 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-50:hover {
  --tw-text-opacity: 1;
  color: rgb(238 242 255 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-500:hover {
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-600:hover {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-700:hover {
  --tw-text-opacity: 1;
  color: rgb(67 56 202 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-800:hover {
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-900:hover {
  --tw-text-opacity: 1;
  color: rgb(49 46 129 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-950:hover {
  --tw-text-opacity: 1;
  color: rgb(30 27 75 / var(--tw-text-opacity, 1));
}

.hover\:text-lime-100:hover {
  --tw-text-opacity: 1;
  color: rgb(236 252 203 / var(--tw-text-opacity, 1));
}

.hover\:text-lime-200:hover {
  --tw-text-opacity: 1;
  color: rgb(217 249 157 / var(--tw-text-opacity, 1));
}

.hover\:text-lime-300:hover {
  --tw-text-opacity: 1;
  color: rgb(190 242 100 / var(--tw-text-opacity, 1));
}

.hover\:text-lime-400:hover {
  --tw-text-opacity: 1;
  color: rgb(163 230 53 / var(--tw-text-opacity, 1));
}

.hover\:text-lime-50:hover {
  --tw-text-opacity: 1;
  color: rgb(247 254 231 / var(--tw-text-opacity, 1));
}

.hover\:text-lime-500:hover {
  --tw-text-opacity: 1;
  color: rgb(132 204 22 / var(--tw-text-opacity, 1));
}

.hover\:text-lime-600:hover {
  --tw-text-opacity: 1;
  color: rgb(101 163 13 / var(--tw-text-opacity, 1));
}

.hover\:text-lime-700:hover {
  --tw-text-opacity: 1;
  color: rgb(77 124 15 / var(--tw-text-opacity, 1));
}

.hover\:text-lime-800:hover {
  --tw-text-opacity: 1;
  color: rgb(63 98 18 / var(--tw-text-opacity, 1));
}

.hover\:text-lime-900:hover {
  --tw-text-opacity: 1;
  color: rgb(54 83 20 / var(--tw-text-opacity, 1));
}

.hover\:text-lime-950:hover {
  --tw-text-opacity: 1;
  color: rgb(26 46 5 / var(--tw-text-opacity, 1));
}

.hover\:text-neutral-100:hover {
  --tw-text-opacity: 1;
  color: rgb(245 245 245 / var(--tw-text-opacity, 1));
}

.hover\:text-neutral-200:hover {
  --tw-text-opacity: 1;
  color: rgb(229 229 229 / var(--tw-text-opacity, 1));
}

.hover\:text-neutral-300:hover {
  --tw-text-opacity: 1;
  color: rgb(212 212 212 / var(--tw-text-opacity, 1));
}

.hover\:text-neutral-400:hover {
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity, 1));
}

.hover\:text-neutral-50:hover {
  --tw-text-opacity: 1;
  color: rgb(250 250 250 / var(--tw-text-opacity, 1));
}

.hover\:text-neutral-500:hover {
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}

.hover\:text-neutral-600:hover {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}

.hover\:text-neutral-700:hover {
  --tw-text-opacity: 1;
  color: rgb(64 64 64 / var(--tw-text-opacity, 1));
}

.hover\:text-neutral-800:hover {
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
}

.hover\:text-neutral-900:hover {
  --tw-text-opacity: 1;
  color: rgb(23 23 23 / var(--tw-text-opacity, 1));
}

.hover\:text-neutral-950:hover {
  --tw-text-opacity: 1;
  color: rgb(10 10 10 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-100:hover {
  --tw-text-opacity: 1;
  color: rgb(255 237 213 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-200:hover {
  --tw-text-opacity: 1;
  color: rgb(254 215 170 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-300:hover {
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-400:hover {
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-50:hover {
  --tw-text-opacity: 1;
  color: rgb(255 247 237 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-500:hover {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-600:hover {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-700:hover {
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-800:hover {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-900:hover {
  --tw-text-opacity: 1;
  color: rgb(124 45 18 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-950:hover {
  --tw-text-opacity: 1;
  color: rgb(67 20 7 / var(--tw-text-opacity, 1));
}

.hover\:text-pink-100:hover {
  --tw-text-opacity: 1;
  color: rgb(252 231 243 / var(--tw-text-opacity, 1));
}

.hover\:text-pink-200:hover {
  --tw-text-opacity: 1;
  color: rgb(251 207 232 / var(--tw-text-opacity, 1));
}

.hover\:text-pink-300:hover {
  --tw-text-opacity: 1;
  color: rgb(249 168 212 / var(--tw-text-opacity, 1));
}

.hover\:text-pink-400:hover {
  --tw-text-opacity: 1;
  color: rgb(244 114 182 / var(--tw-text-opacity, 1));
}

.hover\:text-pink-50:hover {
  --tw-text-opacity: 1;
  color: rgb(253 242 248 / var(--tw-text-opacity, 1));
}

.hover\:text-pink-500:hover {
  --tw-text-opacity: 1;
  color: rgb(236 72 153 / var(--tw-text-opacity, 1));
}

.hover\:text-pink-600:hover {
  --tw-text-opacity: 1;
  color: rgb(219 39 119 / var(--tw-text-opacity, 1));
}

.hover\:text-pink-700:hover {
  --tw-text-opacity: 1;
  color: rgb(190 24 93 / var(--tw-text-opacity, 1));
}

.hover\:text-pink-800:hover {
  --tw-text-opacity: 1;
  color: rgb(157 23 77 / var(--tw-text-opacity, 1));
}

.hover\:text-pink-900:hover {
  --tw-text-opacity: 1;
  color: rgb(131 24 67 / var(--tw-text-opacity, 1));
}

.hover\:text-pink-950:hover {
  --tw-text-opacity: 1;
  color: rgb(80 7 36 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-100:hover {
  --tw-text-opacity: 1;
  color: rgb(243 232 255 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-200:hover {
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-300:hover {
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-400:hover {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-50:hover {
  --tw-text-opacity: 1;
  color: rgb(250 245 255 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-500:hover {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-600:hover {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-700:hover {
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-800:hover {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-900:hover {
  --tw-text-opacity: 1;
  color: rgb(88 28 135 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-950:hover {
  --tw-text-opacity: 1;
  color: rgb(59 7 100 / var(--tw-text-opacity, 1));
}

.hover\:text-red-100:hover {
  --tw-text-opacity: 1;
  color: rgb(254 226 226 / var(--tw-text-opacity, 1));
}

.hover\:text-red-200:hover {
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}

.hover\:text-red-300:hover {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.hover\:text-red-400:hover {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.hover\:text-red-50:hover {
  --tw-text-opacity: 1;
  color: rgb(254 242 242 / var(--tw-text-opacity, 1));
}

.hover\:text-red-500:hover {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.hover\:text-red-600:hover {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.hover\:text-red-700:hover {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.hover\:text-red-800:hover {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.hover\:text-red-900:hover {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}

.hover\:text-red-950:hover {
  --tw-text-opacity: 1;
  color: rgb(69 10 10 / var(--tw-text-opacity, 1));
}

.hover\:text-rose-100:hover {
  --tw-text-opacity: 1;
  color: rgb(255 228 230 / var(--tw-text-opacity, 1));
}

.hover\:text-rose-200:hover {
  --tw-text-opacity: 1;
  color: rgb(254 205 211 / var(--tw-text-opacity, 1));
}

.hover\:text-rose-300:hover {
  --tw-text-opacity: 1;
  color: rgb(253 164 175 / var(--tw-text-opacity, 1));
}

.hover\:text-rose-400:hover {
  --tw-text-opacity: 1;
  color: rgb(251 113 133 / var(--tw-text-opacity, 1));
}

.hover\:text-rose-50:hover {
  --tw-text-opacity: 1;
  color: rgb(255 241 242 / var(--tw-text-opacity, 1));
}

.hover\:text-rose-500:hover {
  --tw-text-opacity: 1;
  color: rgb(244 63 94 / var(--tw-text-opacity, 1));
}

.hover\:text-rose-600:hover {
  --tw-text-opacity: 1;
  color: rgb(225 29 72 / var(--tw-text-opacity, 1));
}

.hover\:text-rose-700:hover {
  --tw-text-opacity: 1;
  color: rgb(190 18 60 / var(--tw-text-opacity, 1));
}

.hover\:text-rose-800:hover {
  --tw-text-opacity: 1;
  color: rgb(159 18 57 / var(--tw-text-opacity, 1));
}

.hover\:text-rose-900:hover {
  --tw-text-opacity: 1;
  color: rgb(136 19 55 / var(--tw-text-opacity, 1));
}

.hover\:text-rose-950:hover {
  --tw-text-opacity: 1;
  color: rgb(76 5 25 / var(--tw-text-opacity, 1));
}

.hover\:text-sky-100:hover {
  --tw-text-opacity: 1;
  color: rgb(224 242 254 / var(--tw-text-opacity, 1));
}

.hover\:text-sky-200:hover {
  --tw-text-opacity: 1;
  color: rgb(186 230 253 / var(--tw-text-opacity, 1));
}

.hover\:text-sky-300:hover {
  --tw-text-opacity: 1;
  color: rgb(125 211 252 / var(--tw-text-opacity, 1));
}

.hover\:text-sky-400:hover {
  --tw-text-opacity: 1;
  color: rgb(56 189 248 / var(--tw-text-opacity, 1));
}

.hover\:text-sky-50:hover {
  --tw-text-opacity: 1;
  color: rgb(240 249 255 / var(--tw-text-opacity, 1));
}

.hover\:text-sky-500:hover {
  --tw-text-opacity: 1;
  color: rgb(14 165 233 / var(--tw-text-opacity, 1));
}

.hover\:text-sky-600:hover {
  --tw-text-opacity: 1;
  color: rgb(2 132 199 / var(--tw-text-opacity, 1));
}

.hover\:text-sky-700:hover {
  --tw-text-opacity: 1;
  color: rgb(3 105 161 / var(--tw-text-opacity, 1));
}

.hover\:text-sky-800:hover {
  --tw-text-opacity: 1;
  color: rgb(7 89 133 / var(--tw-text-opacity, 1));
}

.hover\:text-sky-900:hover {
  --tw-text-opacity: 1;
  color: rgb(12 74 110 / var(--tw-text-opacity, 1));
}

.hover\:text-sky-950:hover {
  --tw-text-opacity: 1;
  color: rgb(8 47 73 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-100:hover {
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-200:hover {
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-300:hover {
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-400:hover {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-50:hover {
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-500:hover {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-600:hover {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-700:hover {
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-900:hover {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}

.hover\:text-slate-950:hover {
  --tw-text-opacity: 1;
  color: rgb(2 6 23 / var(--tw-text-opacity, 1));
}

.hover\:text-stone-100:hover {
  --tw-text-opacity: 1;
  color: rgb(245 245 244 / var(--tw-text-opacity, 1));
}

.hover\:text-stone-200:hover {
  --tw-text-opacity: 1;
  color: rgb(231 229 228 / var(--tw-text-opacity, 1));
}

.hover\:text-stone-300:hover {
  --tw-text-opacity: 1;
  color: rgb(214 211 209 / var(--tw-text-opacity, 1));
}

.hover\:text-stone-400:hover {
  --tw-text-opacity: 1;
  color: rgb(168 162 158 / var(--tw-text-opacity, 1));
}

.hover\:text-stone-50:hover {
  --tw-text-opacity: 1;
  color: rgb(250 250 249 / var(--tw-text-opacity, 1));
}

.hover\:text-stone-500:hover {
  --tw-text-opacity: 1;
  color: rgb(120 113 108 / var(--tw-text-opacity, 1));
}

.hover\:text-stone-600:hover {
  --tw-text-opacity: 1;
  color: rgb(87 83 78 / var(--tw-text-opacity, 1));
}

.hover\:text-stone-700:hover {
  --tw-text-opacity: 1;
  color: rgb(68 64 60 / var(--tw-text-opacity, 1));
}

.hover\:text-stone-800:hover {
  --tw-text-opacity: 1;
  color: rgb(41 37 36 / var(--tw-text-opacity, 1));
}

.hover\:text-stone-900:hover {
  --tw-text-opacity: 1;
  color: rgb(28 25 23 / var(--tw-text-opacity, 1));
}

.hover\:text-stone-950:hover {
  --tw-text-opacity: 1;
  color: rgb(12 10 9 / var(--tw-text-opacity, 1));
}

.hover\:text-teal-100:hover {
  --tw-text-opacity: 1;
  color: rgb(204 251 241 / var(--tw-text-opacity, 1));
}

.hover\:text-teal-200:hover {
  --tw-text-opacity: 1;
  color: rgb(153 246 228 / var(--tw-text-opacity, 1));
}

.hover\:text-teal-300:hover {
  --tw-text-opacity: 1;
  color: rgb(94 234 212 / var(--tw-text-opacity, 1));
}

.hover\:text-teal-400:hover {
  --tw-text-opacity: 1;
  color: rgb(45 212 191 / var(--tw-text-opacity, 1));
}

.hover\:text-teal-50:hover {
  --tw-text-opacity: 1;
  color: rgb(240 253 250 / var(--tw-text-opacity, 1));
}

.hover\:text-teal-500:hover {
  --tw-text-opacity: 1;
  color: rgb(20 184 166 / var(--tw-text-opacity, 1));
}

.hover\:text-teal-600:hover {
  --tw-text-opacity: 1;
  color: rgb(13 148 136 / var(--tw-text-opacity, 1));
}

.hover\:text-teal-700:hover {
  --tw-text-opacity: 1;
  color: rgb(15 118 110 / var(--tw-text-opacity, 1));
}

.hover\:text-teal-800:hover {
  --tw-text-opacity: 1;
  color: rgb(17 94 89 / var(--tw-text-opacity, 1));
}

.hover\:text-teal-900:hover {
  --tw-text-opacity: 1;
  color: rgb(19 78 74 / var(--tw-text-opacity, 1));
}

.hover\:text-teal-950:hover {
  --tw-text-opacity: 1;
  color: rgb(4 47 46 / var(--tw-text-opacity, 1));
}

.hover\:text-tremor-brand-emphasis:hover {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.hover\:text-tremor-content:hover {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.hover\:text-tremor-content-emphasis:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-violet-100:hover {
  --tw-text-opacity: 1;
  color: rgb(237 233 254 / var(--tw-text-opacity, 1));
}

.hover\:text-violet-200:hover {
  --tw-text-opacity: 1;
  color: rgb(221 214 254 / var(--tw-text-opacity, 1));
}

.hover\:text-violet-300:hover {
  --tw-text-opacity: 1;
  color: rgb(196 181 253 / var(--tw-text-opacity, 1));
}

.hover\:text-violet-400:hover {
  --tw-text-opacity: 1;
  color: rgb(167 139 250 / var(--tw-text-opacity, 1));
}

.hover\:text-violet-50:hover {
  --tw-text-opacity: 1;
  color: rgb(245 243 255 / var(--tw-text-opacity, 1));
}

.hover\:text-violet-500:hover {
  --tw-text-opacity: 1;
  color: rgb(139 92 246 / var(--tw-text-opacity, 1));
}

.hover\:text-violet-600:hover {
  --tw-text-opacity: 1;
  color: rgb(124 58 237 / var(--tw-text-opacity, 1));
}

.hover\:text-violet-700:hover {
  --tw-text-opacity: 1;
  color: rgb(109 40 217 / var(--tw-text-opacity, 1));
}

.hover\:text-violet-800:hover {
  --tw-text-opacity: 1;
  color: rgb(91 33 182 / var(--tw-text-opacity, 1));
}

.hover\:text-violet-900:hover {
  --tw-text-opacity: 1;
  color: rgb(76 29 149 / var(--tw-text-opacity, 1));
}

.hover\:text-violet-950:hover {
  --tw-text-opacity: 1;
  color: rgb(46 16 101 / var(--tw-text-opacity, 1));
}

.hover\:text-yellow-100:hover {
  --tw-text-opacity: 1;
  color: rgb(254 249 195 / var(--tw-text-opacity, 1));
}

.hover\:text-yellow-200:hover {
  --tw-text-opacity: 1;
  color: rgb(254 240 138 / var(--tw-text-opacity, 1));
}

.hover\:text-yellow-300:hover {
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}

.hover\:text-yellow-400:hover {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}

.hover\:text-yellow-50:hover {
  --tw-text-opacity: 1;
  color: rgb(254 252 232 / var(--tw-text-opacity, 1));
}

.hover\:text-yellow-500:hover {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}

.hover\:text-yellow-600:hover {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}

.hover\:text-yellow-700:hover {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}

.hover\:text-yellow-800:hover {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}

.hover\:text-yellow-900:hover {
  --tw-text-opacity: 1;
  color: rgb(113 63 18 / var(--tw-text-opacity, 1));
}

.hover\:text-yellow-950:hover {
  --tw-text-opacity: 1;
  color: rgb(66 32 6 / var(--tw-text-opacity, 1));
}

.hover\:text-zinc-100:hover {
  --tw-text-opacity: 1;
  color: rgb(244 244 245 / var(--tw-text-opacity, 1));
}

.hover\:text-zinc-200:hover {
  --tw-text-opacity: 1;
  color: rgb(228 228 231 / var(--tw-text-opacity, 1));
}

.hover\:text-zinc-300:hover {
  --tw-text-opacity: 1;
  color: rgb(212 212 216 / var(--tw-text-opacity, 1));
}

.hover\:text-zinc-400:hover {
  --tw-text-opacity: 1;
  color: rgb(161 161 170 / var(--tw-text-opacity, 1));
}

.hover\:text-zinc-50:hover {
  --tw-text-opacity: 1;
  color: rgb(250 250 250 / var(--tw-text-opacity, 1));
}

.hover\:text-zinc-500:hover {
  --tw-text-opacity: 1;
  color: rgb(113 113 122 / var(--tw-text-opacity, 1));
}

.hover\:text-zinc-600:hover {
  --tw-text-opacity: 1;
  color: rgb(82 82 91 / var(--tw-text-opacity, 1));
}

.hover\:text-zinc-700:hover {
  --tw-text-opacity: 1;
  color: rgb(63 63 70 / var(--tw-text-opacity, 1));
}

.hover\:text-zinc-800:hover {
  --tw-text-opacity: 1;
  color: rgb(39 39 42 / var(--tw-text-opacity, 1));
}

.hover\:text-zinc-900:hover {
  --tw-text-opacity: 1;
  color: rgb(24 24 27 / var(--tw-text-opacity, 1));
}

.hover\:text-zinc-950:hover {
  --tw-text-opacity: 1;
  color: rgb(9 9 11 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:border-tremor-brand-subtle:focus {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-tremor-brand-muted:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(191 219 254 / var(--tw-ring-opacity, 1));
}

.disabled\:hover\:bg-transparent:hover:disabled {
  background-color: transparent;
}

.group:hover .group-hover\:bg-tremor-brand-subtle\/30 {
  background-color: rgb(96 165 250 / 0.3);
}

.group:hover .group-hover\:bg-opacity-30 {
  --tw-bg-opacity: 0.3;
}

.group:hover .group-hover\:text-tremor-content-emphasis {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.group:active .group-active\:scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.aria-selected\:\!bg-tremor-background-subtle[aria-selected="true"] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1)) !important;
}

.aria-selected\:bg-tremor-background-emphasis[aria-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.aria-selected\:\!text-tremor-content[aria-selected="true"] {
  --tw-text-opacity: 1 !important;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1)) !important;
}

.aria-selected\:text-tremor-brand-inverted[aria-selected="true"] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.aria-selected\:text-tremor-content-inverted[aria-selected="true"] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.data-\[selected\]\:border-b-2[data-selected] {
  border-bottom-width: 2px;
}

.data-\[selected\]\:border-tremor-border[data-selected] {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.data-\[selected\]\:border-tremor-brand[data-selected] {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.data-\[focus\]\:bg-tremor-background-muted[data-focus] {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.data-\[selected\]\:bg-tremor-background[data-selected] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.data-\[selected\]\:bg-tremor-background-muted[data-selected] {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.data-\[focus\]\:text-tremor-content-strong[data-focus] {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.data-\[selected\]\:text-tremor-brand[data-selected] {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.data-\[selected\]\:text-tremor-content-strong[data-selected] {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.data-\[closed\]\:opacity-0[data-closed] {
  opacity: 0;
}

.data-\[enter\]\:duration-300[data-enter] {
  transition-duration: 300ms;
}

.data-\[leave\]\:duration-200[data-leave] {
  transition-duration: 200ms;
}

.data-\[enter\]\:ease-out[data-enter] {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.data-\[leave\]\:ease-in[data-leave] {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

@media (min-width: 640px) {

  .sm\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .sm\:col-span-10 {
    grid-column: span 10 / span 10;
  }

  .sm\:col-span-11 {
    grid-column: span 11 / span 11;
  }

  .sm\:col-span-12 {
    grid-column: span 12 / span 12;
  }

  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .sm\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .sm\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .sm\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .sm\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .sm\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .sm\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .sm\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .sm\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .sm\:grid-cols-10 {
    grid-template-columns: repeat(10, minmax(0, 1fr));
  }

  .sm\:grid-cols-11 {
    grid-template-columns: repeat(11, minmax(0, 1fr));
  }

  .sm\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .sm\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .sm\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .sm\:grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .sm\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }

  .sm\:grid-cols-9 {
    grid-template-columns: repeat(9, minmax(0, 1fr));
  }

  .sm\:grid-cols-none {
    grid-template-columns: none;
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
}

@media (min-width: 768px) {

  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .md\:col-span-10 {
    grid-column: span 10 / span 10;
  }

  .md\:col-span-11 {
    grid-column: span 11 / span 11;
  }

  .md\:col-span-12 {
    grid-column: span 12 / span 12;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .md\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .md\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .md\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .md\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .md\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .md\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .md\:grid-cols-10 {
    grid-template-columns: repeat(10, minmax(0, 1fr));
  }

  .md\:grid-cols-11 {
    grid-template-columns: repeat(11, minmax(0, 1fr));
  }

  .md\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .md\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .md\:grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .md\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }

  .md\:grid-cols-9 {
    grid-template-columns: repeat(9, minmax(0, 1fr));
  }

  .md\:grid-cols-none {
    grid-template-columns: none;
  }
}

@media (min-width: 1024px) {

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-10 {
    grid-column: span 10 / span 10;
  }

  .lg\:col-span-11 {
    grid-column: span 11 / span 11;
  }

  .lg\:col-span-12 {
    grid-column: span 12 / span 12;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .lg\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .lg\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .lg\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .lg\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .lg\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .lg\:max-w-\[200px\] {
    max-width: 200px;
  }

  .lg\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .lg\:grid-cols-10 {
    grid-template-columns: repeat(10, minmax(0, 1fr));
  }

  .lg\:grid-cols-11 {
    grid-template-columns: repeat(11, minmax(0, 1fr));
  }

  .lg\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .lg\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }

  .lg\:grid-cols-9 {
    grid-template-columns: repeat(9, minmax(0, 1fr));
  }

  .lg\:grid-cols-none {
    grid-template-columns: none;
  }
}

@media (prefers-color-scheme: dark) {

  .dark\:border-red-500 {
    --tw-border-opacity: 1;
    border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
  }

  .dark\:bg-slate-950\/50 {
    background-color: rgb(2 6 23 / 0.5);
  }

  .dark\:bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-opacity-10 {
    --tw-bg-opacity: 0.1;
  }

  .dark\:bg-opacity-5 {
    --tw-bg-opacity: 0.05;
  }

  .dark\:text-red-500 {
    --tw-text-opacity: 1;
    color: rgb(239 68 68 / var(--tw-text-opacity, 1));
  }

  .dark\:text-tremor-content-emphasis {
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity, 1));
  }

  .dark\:opacity-25 {
    opacity: 0.25;
  }

  .dark\:ring-opacity-60 {
    --tw-ring-opacity: 0.6;
  }

  .dark\:placeholder\:text-red-500::-moz-placeholder {
    --tw-text-opacity: 1;
    color: rgb(239 68 68 / var(--tw-text-opacity, 1));
  }

  .dark\:placeholder\:text-red-500::placeholder {
    --tw-text-opacity: 1;
    color: rgb(239 68 68 / var(--tw-text-opacity, 1));
  }

  .dark\:placeholder\:text-tremor-content::-moz-placeholder {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity, 1));
  }

  .dark\:placeholder\:text-tremor-content::placeholder {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity, 1));
  }

  .dark\:placeholder\:text-tremor-content-subtle::-moz-placeholder {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  }

  .dark\:placeholder\:text-tremor-content-subtle::placeholder {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:bg-opacity-20:hover {
    --tw-bg-opacity: 0.2;
  }

  .dark\:hover\:text-tremor-content:hover {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:text-tremor-content-emphasis:hover {
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity, 1));
  }
}

.\[\&\:\:-webkit-inner-spin-button\]\:appearance-none::-webkit-inner-spin-button {
  -webkit-appearance: none;
          appearance: none;
}

.\[\&\:\:-webkit-outer-spin-button\]\:appearance-none::-webkit-outer-spin-button {
  -webkit-appearance: none;
          appearance: none;
}

.\[\&\:\:-webkit-scrollbar\]\:hidden::-webkit-scrollbar {
  display: none;
}

.\[\&\:not\(\[data-selected\]\)\]\:text-tremor-content:not([data-selected]) {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.\[\&\:not\(\[data-selected\]\)\]\:hover\:text-tremor-content-emphasis:hover:not([data-selected]) {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
