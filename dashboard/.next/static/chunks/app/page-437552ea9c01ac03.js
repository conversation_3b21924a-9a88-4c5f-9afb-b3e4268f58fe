(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{3611:function(e,s,i){Promise.resolve().then(i.bind(i,5803))},5803:function(e,s,i){"use strict";i.r(s),i.d(s,{default:function(){return h}});var l=i(7437),c=i(2265),r=i(1649),a=i(8489),t=i(2514),n=i(5829),d=i(4264),o=i(2967),x=i(6761);function h(){let[e,s]=(0,c.useState)([]),[i,h]=(0,c.useState)(!0),[m,j]=(0,c.useState)(null),[g,p]=(0,c.useState)(""),[u,N]=(0,c.useState)(null);(0,c.useEffect)(()=>{(async()=>{try{h(!0);let e=await fetch("/api/policies");if(!e.ok)throw Error("Failed to fetch policies");let i=await e.json();s(i)}catch(e){j(e instanceof Error?e.message:"An error occurred")}finally{h(!1)}})()},[]);let y=e.filter(e=>e.short_title.toLowerCase().includes(g.toLowerCase())||e.description.toLowerCase().includes(g.toLowerCase())||e.policy_id.toString().includes(g));return i?(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,l.jsx)(d.Z,{children:"Loading policies..."})]})}):m?(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsxs)(d.Z,{className:"text-red-600 mb-4",children:["Error: ",m]}),(0,l.jsx)(a.Z,{onClick:()=>window.location.reload(),children:"Retry"})]})}):u?(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 p-4",children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,l.jsx)("div",{className:"mb-4",children:(0,l.jsx)(a.Z,{onClick:()=>N(null),children:"← Back to Dashboard"})}),(0,l.jsx)(t.Z,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)(r.Z,{color:"blue",children:["Policy #",u.policy_id]}),(0,l.jsx)(x.Z,{className:"text-2xl mt-2",children:u.short_title})]}),(0,l.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,l.jsxs)(t.Z,{children:[(0,l.jsx)(d.Z,{children:"Perplexity Length"}),(0,l.jsx)(n.Z,{children:u.source_analysis.perplexity_content_length})]}),(0,l.jsxs)(t.Z,{children:[(0,l.jsx)(d.Z,{children:"RAG Length"}),(0,l.jsx)(n.Z,{children:u.source_analysis.rag_content_length})]}),(0,l.jsxs)(t.Z,{children:[(0,l.jsx)(d.Z,{children:"Citations"}),(0,l.jsx)(n.Z,{children:u.source_analysis.citations_found})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(d.Z,{className:"font-semibold",children:"Description:"}),(0,l.jsx)(d.Z,{className:"mt-2",children:u.description})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(d.Z,{className:"font-semibold",children:"Original Title:"}),(0,l.jsx)(d.Z,{className:"mt-2 text-sm text-gray-600",children:u.policy_title})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(d.Z,{className:"font-semibold",children:"Perplexity Analysis:"}),(0,l.jsx)(d.Z,{className:"mt-2 text-sm",children:u.source_analysis.perplexity_description})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(d.Z,{className:"font-semibold",children:"RAG Context:"}),(0,l.jsx)(d.Z,{className:"mt-2 text-sm",children:u.source_analysis.rag_description})]})]})})]})}):(0,l.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:[(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)(x.Z,{className:"text-3xl font-bold mb-2",children:"Project 2025 Policy Dashboard"}),(0,l.jsx)(d.Z,{className:"text-gray-600",children:"Interactive dashboard for viewing and analyzing processed policies"}),(0,l.jsxs)("div",{className:"flex gap-2 mt-4",children:[(0,l.jsxs)(r.Z,{color:"blue",children:[e.length," Policies Processed"]}),(0,l.jsx)(r.Z,{color:"emerald",children:"Enhanced RAG v2"}),(0,l.jsx)(r.Z,{color:"purple",children:"Short Titles"})]})]}),(0,l.jsx)("div",{className:"mb-6",children:(0,l.jsx)(o.Z,{placeholder:"Search policies by ID, title, or description...",value:g,onValueChange:p,className:"max-w-md"})}),(0,l.jsx)("div",{className:"mb-4",children:(0,l.jsxs)(d.Z,{children:["Showing ",y.length," of ",e.length," policies"]})}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:y.map(e=>(0,l.jsx)(t.Z,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>N(e),children:(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsxs)(r.Z,{color:"blue",children:["#",e.policy_id]}),(0,l.jsx)(d.Z,{className:"text-xs text-gray-500",children:new Date(e.processing_metadata.processing_timestamp).toLocaleDateString()})]}),(0,l.jsx)(x.Z,{className:"text-lg",children:e.short_title}),(0,l.jsx)(d.Z,{className:"text-sm text-gray-600 line-clamp-2",children:e.description}),(0,l.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(n.Z,{className:"text-sm",children:e.source_analysis.perplexity_content_length}),(0,l.jsx)(d.Z,{className:"text-xs",children:"Perplexity"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(n.Z,{className:"text-sm",children:e.source_analysis.rag_content_length}),(0,l.jsx)(d.Z,{className:"text-xs",children:"RAG"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(n.Z,{className:"text-sm",children:e.source_analysis.citations_found}),(0,l.jsx)(d.Z,{className:"text-xs",children:"Citations"})]})]})]})},e.policy_id))}),0===y.length&&(0,l.jsx)("div",{className:"text-center py-12",children:(0,l.jsx)(d.Z,{className:"text-gray-500",children:"No policies found matching your search criteria."})})]})})}}},function(e){e.O(0,[880,971,117,744],function(){return e(e.s=3611)}),_N_E=e.O()}]);