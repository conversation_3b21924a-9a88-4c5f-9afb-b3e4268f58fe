"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[880],{1649:function(e,t,r){r.d(t,{Z:function(){return p}});var n=r(6200),o=r(2265),l=r(1526),i=r(7084),a=r(6898),s=r(4157),c=r(1153);let d={xs:{paddingX:"px-2",paddingY:"py-0.5",fontSize:"text-xs"},sm:{paddingX:"px-2.5",paddingY:"py-0.5",fontSize:"text-sm"},md:{paddingX:"px-3",paddingY:"py-0.5",fontSize:"text-md"},lg:{paddingX:"px-3.5",paddingY:"py-0.5",fontSize:"text-lg"},xl:{paddingX:"px-4",paddingY:"py-1",fontSize:"text-xl"}},u={xs:{height:"h-4",width:"w-4"},sm:{height:"h-4",width:"w-4"},md:{height:"h-4",width:"w-4"},lg:{height:"h-5",width:"w-5"},xl:{height:"h-6",width:"w-6"}},f=(0,c.fn)("Badge"),p=o.forwardRef((e,t)=>{let{color:r,icon:p,size:m=i.u8.SM,tooltip:g,className:b,children:h}=e,v=(0,n._T)(e,["color","icon","size","tooltip","className","children"]),x=p||null,{tooltipProps:y,getReferenceProps:w}=(0,l.l)();return o.createElement("span",Object.assign({ref:(0,c.lq)([t,y.refs.setReference]),className:(0,s.q)(f("root"),"w-max shrink-0 inline-flex justify-center items-center cursor-default rounded-tremor-small ring-1 ring-inset",r?(0,s.q)((0,c.bM)(r,a.K.background).bgColor,(0,c.bM)(r,a.K.iconText).textColor,(0,c.bM)(r,a.K.iconRing).ringColor,"bg-opacity-10 ring-opacity-20","dark:bg-opacity-5 dark:ring-opacity-60"):(0,s.q)("bg-tremor-brand-faint text-tremor-brand-emphasis ring-tremor-brand/20","dark:bg-dark-tremor-brand-muted/50 dark:text-dark-tremor-brand dark:ring-dark-tremor-subtle/20"),d[m].paddingX,d[m].paddingY,d[m].fontSize,b)},w,v),o.createElement(l.Z,Object.assign({text:g},y)),x?o.createElement(x,{className:(0,s.q)(f("icon"),"shrink-0 -ml-1 mr-1.5",u[m].height,u[m].width)}):null,o.createElement("span",{className:(0,s.q)(f("text"),"whitespace-nowrap")},h))});p.displayName="Badge"},8489:function(e,t,r){r.d(t,{Z:function(){return E}});var n=r(6200),o=r(1526),l=r(2265);let i=["preEnter","entering","entered","preExit","exiting","exited","unmounted"],a=e=>({_s:e,status:i[e],isEnter:e<3,isMounted:6!==e,isResolved:2===e||e>4}),s=e=>e?6:5,c=(e,t)=>{switch(e){case 1:case 0:return 2;case 4:case 3:return s(t)}},d=e=>"object"==typeof e?[e.enter,e.exit]:[e,e],u=(e,t)=>setTimeout(()=>{isNaN(document.body.offsetTop)||e(t+1)},0),f=(e,t,r,n,o)=>{clearTimeout(n.current);let l=a(e);t(l),r.current=l,o&&o({current:l})},p=({enter:e=!0,exit:t=!0,preEnter:r,preExit:n,timeout:o,initialEntered:i,mountOnEnter:p,unmountOnExit:m,onStateChange:g}={})=>{let[b,h]=(0,l.useState)(()=>a(i?2:s(p))),v=(0,l.useRef)(b),x=(0,l.useRef)(),[y,w]=d(o),k=(0,l.useCallback)(()=>{let e=c(v.current._s,m);e&&f(e,h,v,x,g)},[g,m]);return[b,(0,l.useCallback)(o=>{let l=e=>{switch(f(e,h,v,x,g),e){case 1:y>=0&&(x.current=setTimeout(k,y));break;case 4:w>=0&&(x.current=setTimeout(k,w));break;case 0:case 3:x.current=u(l,e)}},i=v.current.isEnter;"boolean"!=typeof o&&(o=!i),o?i||l(e?r?0:1:2):i&&l(t?n?3:4:s(m))},[k,g,e,t,r,n,y,w,m]),k]};var m=r(7084),g=r(4157),b=r(1153);let h=e=>{var t=(0,n._T)(e,[]);return l.createElement("svg",Object.assign({},t,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor"}),l.createElement("path",{fill:"none",d:"M0 0h24v24H0z"}),l.createElement("path",{d:"M18.364 5.636L16.95 7.05A7 7 0 1 0 19 12h2a9 9 0 1 1-2.636-6.364z"}))};var v=r(6898);let x={xs:{height:"h-4",width:"w-4"},sm:{height:"h-5",width:"w-5"},md:{height:"h-5",width:"w-5"},lg:{height:"h-6",width:"w-6"},xl:{height:"h-6",width:"w-6"}},y=e=>"light"!==e?{xs:{paddingX:"px-2.5",paddingY:"py-1.5",fontSize:"text-xs"},sm:{paddingX:"px-4",paddingY:"py-2",fontSize:"text-sm"},md:{paddingX:"px-4",paddingY:"py-2",fontSize:"text-md"},lg:{paddingX:"px-4",paddingY:"py-2.5",fontSize:"text-lg"},xl:{paddingX:"px-4",paddingY:"py-3",fontSize:"text-xl"}}:{xs:{paddingX:"",paddingY:"",fontSize:"text-xs"},sm:{paddingX:"",paddingY:"",fontSize:"text-sm"},md:{paddingX:"",paddingY:"",fontSize:"text-md"},lg:{paddingX:"",paddingY:"",fontSize:"text-lg"},xl:{paddingX:"",paddingY:"",fontSize:"text-xl"}},w=(e,t)=>{switch(e){case"primary":return{textColor:t?(0,b.bM)("white").textColor:"text-tremor-brand-inverted dark:text-dark-tremor-brand-inverted",hoverTextColor:t?(0,b.bM)("white").textColor:"text-tremor-brand-inverted dark:text-dark-tremor-brand-inverted",bgColor:t?(0,b.bM)(t,v.K.background).bgColor:"bg-tremor-brand dark:bg-dark-tremor-brand",hoverBgColor:t?(0,b.bM)(t,v.K.darkBackground).hoverBgColor:"hover:bg-tremor-brand-emphasis dark:hover:bg-dark-tremor-brand-emphasis",borderColor:t?(0,b.bM)(t,v.K.border).borderColor:"border-tremor-brand dark:border-dark-tremor-brand",hoverBorderColor:t?(0,b.bM)(t,v.K.darkBorder).hoverBorderColor:"hover:border-tremor-brand-emphasis dark:hover:border-dark-tremor-brand-emphasis"};case"secondary":return{textColor:t?(0,b.bM)(t,v.K.text).textColor:"text-tremor-brand dark:text-dark-tremor-brand",hoverTextColor:t?(0,b.bM)(t,v.K.text).textColor:"hover:text-tremor-brand-emphasis dark:hover:text-dark-tremor-brand-emphasis",bgColor:(0,b.bM)("transparent").bgColor,hoverBgColor:t?(0,g.q)((0,b.bM)(t,v.K.background).hoverBgColor,"hover:bg-opacity-20 dark:hover:bg-opacity-20"):"hover:bg-tremor-brand-faint dark:hover:bg-dark-tremor-brand-faint",borderColor:t?(0,b.bM)(t,v.K.border).borderColor:"border-tremor-brand dark:border-dark-tremor-brand"};case"light":return{textColor:t?(0,b.bM)(t,v.K.text).textColor:"text-tremor-brand dark:text-dark-tremor-brand",hoverTextColor:t?(0,b.bM)(t,v.K.darkText).hoverTextColor:"hover:text-tremor-brand-emphasis dark:hover:text-dark-tremor-brand-emphasis",bgColor:(0,b.bM)("transparent").bgColor,borderColor:"",hoverBorderColor:""}}},k=(0,b.fn)("Button"),C=e=>{let{loading:t,iconSize:r,iconPosition:n,Icon:o,needMargin:i,transitionStatus:a}=e,s=i?n===m.zS.Left?(0,g.q)("-ml-1","mr-1.5"):(0,g.q)("-mr-1","ml-1.5"):"",c=(0,g.q)("w-0 h-0"),d={default:c,entering:c,entered:r,exiting:r,exited:c};return t?l.createElement(h,{className:(0,g.q)(k("icon"),"animate-spin shrink-0",s,d.default,d[a]),style:{transition:"width 150ms"}}):l.createElement(o,{className:(0,g.q)(k("icon"),"shrink-0",r,s)})},E=l.forwardRef((e,t)=>{let{icon:r,iconPosition:i=m.zS.Left,size:a=m.u8.SM,color:s,variant:c="primary",disabled:d,loading:u=!1,loadingText:f,children:h,tooltip:v,className:E}=e,R=(0,n._T)(e,["icon","iconPosition","size","color","variant","disabled","loading","loadingText","children","tooltip","className"]),T=u||d,S=void 0!==r||u,z=u&&f,M=!(!h&&!z),L=(0,g.q)(x[a].height,x[a].width),P="light"!==c?(0,g.q)("rounded-tremor-default border","shadow-tremor-input","dark:shadow-dark-tremor-input"):"",N=w(c,s),B=y(c)[a],{tooltipProps:O,getReferenceProps:j}=(0,o.l)(300),[q,A]=p({timeout:50});return(0,l.useEffect)(()=>{A(u)},[u]),l.createElement("button",Object.assign({ref:(0,b.lq)([t,O.refs.setReference]),className:(0,g.q)(k("root"),"shrink-0 inline-flex justify-center items-center group font-medium outline-none",P,B.paddingX,B.paddingY,B.fontSize,N.textColor,N.bgColor,N.borderColor,N.hoverBorderColor,T?"opacity-50 cursor-not-allowed":(0,g.q)(w(c,s).hoverTextColor,w(c,s).hoverBgColor,w(c,s).hoverBorderColor),E),disabled:T},j,R),l.createElement(o.Z,Object.assign({text:v},O)),S&&i!==m.zS.Right?l.createElement(C,{loading:u,iconSize:L,iconPosition:i,Icon:r,transitionStatus:q.status,needMargin:M}):null,z||h?l.createElement("span",{className:(0,g.q)(k("text"),"text-tremor-default whitespace-nowrap")},z?f:h):null,S&&i===m.zS.Right?l.createElement(C,{loading:u,iconSize:L,iconPosition:i,Icon:r,transitionStatus:q.status,needMargin:M}):null)});E.displayName="Button"},2967:function(e,t,r){r.d(t,{Z:function(){return m}});var n=r(6200),o=r(2265),l=r(4157),i=r(1153);let a=e=>{var t=(0,n._T)(e,[]);return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor"},t),o.createElement("path",{d:"M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM11 15V17H13V15H11ZM11 7V13H13V7H11Z"}))},s=e=>{var t=(0,n._T)(e,[]);return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor"},t),o.createElement("path",{d:"M1.18164 12C2.12215 6.87976 6.60812 3 12.0003 3C17.3924 3 21.8784 6.87976 22.8189 12C21.8784 17.1202 17.3924 21 12.0003 21C6.60812 21 2.12215 17.1202 1.18164 12ZM12.0003 17C14.7617 17 17.0003 14.7614 17.0003 12C17.0003 9.23858 14.7617 7 12.0003 7C9.23884 7 7.00026 9.23858 7.00026 12C7.00026 14.7614 9.23884 17 12.0003 17ZM12.0003 15C10.3434 15 9.00026 13.6569 9.00026 12C9.00026 10.3431 10.3434 9 12.0003 9C13.6571 9 15.0003 10.3431 15.0003 12C15.0003 13.6569 13.6571 15 12.0003 15Z"}))},c=e=>{var t=(0,n._T)(e,[]);return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor"},t),o.createElement("path",{d:"M4.52047 5.93457L1.39366 2.80777L2.80788 1.39355L22.6069 21.1925L21.1927 22.6068L17.8827 19.2968C16.1814 20.3755 14.1638 21.0002 12.0003 21.0002C6.60812 21.0002 2.12215 17.1204 1.18164 12.0002C1.61832 9.62282 2.81932 7.5129 4.52047 5.93457ZM14.7577 16.1718L13.2937 14.7078C12.902 14.8952 12.4634 15.0002 12.0003 15.0002C10.3434 15.0002 9.00026 13.657 9.00026 12.0002C9.00026 11.537 9.10522 11.0984 9.29263 10.7067L7.82866 9.24277C7.30514 10.0332 7.00026 10.9811 7.00026 12.0002C7.00026 14.7616 9.23884 17.0002 12.0003 17.0002C13.0193 17.0002 13.9672 16.6953 14.7577 16.1718ZM7.97446 3.76015C9.22127 3.26959 10.5793 3.00016 12.0003 3.00016C17.3924 3.00016 21.8784 6.87992 22.8189 12.0002C22.5067 13.6998 21.8038 15.2628 20.8068 16.5925L16.947 12.7327C16.9821 12.4936 17.0003 12.249 17.0003 12.0002C17.0003 9.23873 14.7617 7.00016 12.0003 7.00016C11.7514 7.00016 11.5068 7.01833 11.2677 7.05343L7.97446 3.76015Z"}))},d=e=>["string","number"].includes(typeof e)?e:e instanceof Array?e.map(d).join(""):"object"==typeof e&&e?d(e.props.children):void 0,u=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return(0,l.q)(t?"bg-tremor-background-subtle dark:bg-dark-tremor-background-subtle":"bg-tremor-background dark:bg-dark-tremor-background",!t&&"hover:bg-tremor-background-muted dark:hover:bg-dark-tremor-background-muted",e?"text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis":"text-tremor-content dark:text-dark-tremor-content",t&&"text-tremor-content-subtle dark:text-dark-tremor-content-subtle",r&&"text-red-500 placeholder:text-red-500 dark:text-red-500 dark:placeholder:text-red-500",r?"border-red-500 dark:border-red-500":"border-tremor-border dark:border-dark-tremor-border")},f=o.forwardRef((e,t)=>{var r;let{value:d,defaultValue:f,type:p,placeholder:m="Type...",icon:g,error:b=!1,errorMessage:h,disabled:v=!1,stepper:x,makeInputClassName:y,className:w,onChange:k,onValueChange:C,autoFocus:E,pattern:R}=e,T=(0,n._T)(e,["value","defaultValue","type","placeholder","icon","error","errorMessage","disabled","stepper","makeInputClassName","className","onChange","onValueChange","autoFocus","pattern"]),[S,z]=(0,o.useState)(E||!1),[M,L]=(0,o.useState)(!1),P=(0,o.useCallback)(()=>L(!M),[M,L]),N=(0,o.useRef)(null),B=null!=(r=d||f)&&""!==r;return o.useEffect(()=>{let e=()=>z(!0),t=()=>z(!1),r=N.current;return r&&(r.addEventListener("focus",e),r.addEventListener("blur",t),E&&r.focus()),()=>{r&&(r.removeEventListener("focus",e),r.removeEventListener("blur",t))}},[E]),o.createElement(o.Fragment,null,o.createElement("div",{className:(0,l.q)(y("root"),"relative w-full flex items-center min-w-[10rem] outline-none rounded-tremor-default transition duration-100 border","shadow-tremor-input","dark:shadow-dark-tremor-input",u(B,v,b),S&&(0,l.q)("ring-2","border-tremor-brand-subtle ring-tremor-brand-muted","dark:border-dark-tremor-brand-subtle dark:ring-dark-tremor-brand-muted"),w)},g?o.createElement(g,{className:(0,l.q)(y("icon"),"shrink-0 h-5 w-5 mx-2.5 absolute left-0 flex items-center","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")}):null,o.createElement("input",Object.assign({ref:(0,i.lq)([N,t]),defaultValue:f,value:d,type:M?"text":p,className:(0,l.q)(y("input"),"w-full bg-transparent focus:outline-none focus:ring-0 border-none text-tremor-default rounded-tremor-default transition duration-100 py-2","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis","[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none","password"===p?b?"pr-16":"pr-12":b?"pr-8":"pr-3",g?"pl-10":"pl-3",v?"placeholder:text-tremor-content-subtle dark:placeholder:text-dark-tremor-content-subtle":"placeholder:text-tremor-content dark:placeholder:text-dark-tremor-content"),placeholder:m,disabled:v,"data-testid":"base-input",onChange:e=>{null==k||k(e),null==C||C(e.target.value)},pattern:R},T)),"password"!==p||v?null:o.createElement("button",{className:(0,l.q)(y("toggleButton"),"absolute inset-y-0 right-0 flex items-center px-2.5 rounded-lg"),type:"button",onClick:()=>P(),"aria-label":M?"Hide password":"Show Password"},M?o.createElement(c,{className:(0,l.q)("flex-none h-5 w-5 transition","text-tremor-content-subtle hover:text-tremor-content","dark:text-dark-tremor-content-subtle hover:dark:text-dark-tremor-content"),"aria-hidden":!0}):o.createElement(s,{className:(0,l.q)("flex-none h-5 w-5 transition","text-tremor-content-subtle hover:text-tremor-content","dark:text-dark-tremor-content-subtle hover:dark:text-dark-tremor-content"),"aria-hidden":!0})),b?o.createElement(a,{className:(0,l.q)(y("errorIcon"),"text-red-500 shrink-0 h-5 w-5 absolute right-0 flex items-center","password"===p?"mr-10":"number"===p?x?"mr-20":"mr-3":"mx-2.5")}):null,null!=x?x:null),b&&h?o.createElement("p",{className:(0,l.q)(y("errorMessage"),"text-sm text-red-500 mt-1")},h):null)});f.displayName="BaseInput";let p=(0,i.fn)("TextInput"),m=o.forwardRef((e,t)=>{let{type:r="text"}=e,l=(0,n._T)(e,["type"]);return o.createElement(f,Object.assign({ref:t,type:r,makeInputClassName:p},l))});m.displayName="TextInput"},2514:function(e,t,r){r.d(t,{Z:function(){return u}});var n=r(6200),o=r(2265),l=r(7084),i=r(6898),a=r(4157),s=r(1153);let c=(0,s.fn)("Card"),d=e=>{if(!e)return"";switch(e){case l.zS.Left:return"border-l-4";case l.m.Top:return"border-t-4";case l.zS.Right:return"border-r-4";case l.m.Bottom:return"border-b-4";default:return""}},u=o.forwardRef((e,t)=>{let{decoration:r="",decorationColor:l,children:u,className:f}=e,p=(0,n._T)(e,["decoration","decorationColor","children","className"]);return o.createElement("div",Object.assign({ref:t,className:(0,a.q)(c("root"),"relative w-full text-left ring-1 rounded-tremor-default p-6","bg-tremor-background ring-tremor-ring shadow-tremor-card","dark:bg-dark-tremor-background dark:ring-dark-tremor-ring dark:shadow-dark-tremor-card",l?(0,s.bM)(l,i.K.border).borderColor:"border-tremor-brand dark:border-dark-tremor-brand",d(r),f)},p),u)});u.displayName="Card"},5829:function(e,t,r){r.d(t,{Z:function(){return s}});var n=r(6200),o=r(6898),l=r(4157),i=r(1153),a=r(2265);let s=a.forwardRef((e,t)=>{let{color:r,children:s,className:c}=e,d=(0,n._T)(e,["color","children","className"]);return a.createElement("p",Object.assign({ref:t,className:(0,l.q)("font-semibold text-tremor-metric",r?(0,i.bM)(r,o.K.darkText).textColor:"text-tremor-content-strong dark:text-dark-tremor-content-strong",c)},d),s)});s.displayName="Metric"},4264:function(e,t,r){r.d(t,{Z:function(){return a}});var n=r(6898),o=r(4157),l=r(1153),i=r(2265);let a=i.forwardRef((e,t)=>{let{color:r,className:a,children:s}=e;return i.createElement("p",{ref:t,className:(0,o.q)("text-tremor-default",r?(0,l.bM)(r,n.K.text).textColor:(0,o.q)("text-tremor-content","dark:text-dark-tremor-content"),a)},s)});a.displayName="Text"},6761:function(e,t,r){r.d(t,{Z:function(){return s}});var n=r(6200),o=r(6898),l=r(4157),i=r(1153),a=r(2265);let s=a.forwardRef((e,t)=>{let{color:r,children:s,className:c}=e,d=(0,n._T)(e,["color","children","className"]);return a.createElement("p",Object.assign({ref:t,className:(0,l.q)("font-medium text-tremor-title",r?(0,i.bM)(r,o.K.darkText).textColor:"text-tremor-content-strong dark:text-dark-tremor-content-strong",c)},d),s)});s.displayName="Title"},1526:function(e,t,r){r.d(t,{Z:function(){return eK},l:function(){return eV}});var n=r(2265),o=r.t(n,2),l=r(4887);function i(){return"undefined"!=typeof window}function a(e){return d(e)?(e.nodeName||"").toLowerCase():"#document"}function s(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function c(e){var t;return null==(t=(d(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function d(e){return!!i()&&(e instanceof Node||e instanceof s(e).Node)}function u(e){return!!i()&&(e instanceof Element||e instanceof s(e).Element)}function f(e){return!!i()&&(e instanceof HTMLElement||e instanceof s(e).HTMLElement)}function p(e){return!!i()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof s(e).ShadowRoot)}function m(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=x(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function g(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function b(e){let t=h(),r=u(e)?x(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function h(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function v(e){return["html","body","#document"].includes(a(e))}function x(e){return s(e).getComputedStyle(e)}function y(e){return u(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function w(e){if("html"===a(e))return e;let t=e.assignedSlot||e.parentNode||p(e)&&e.host||c(e);return p(t)?t.host:t}function k(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=w(t);return v(r)?t.ownerDocument?t.ownerDocument.body:t.body:f(r)&&m(r)?r:e(r)}(e),l=o===(null==(n=e.ownerDocument)?void 0:n.body),i=s(o);if(l){let e=C(i);return t.concat(i,i.visualViewport||[],m(o)?o:[],e&&r?k(e):[])}return t.concat(o,k(o,[],r))}function C(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}let E=Math.min,R=Math.max,T=Math.round,S=Math.floor,z=e=>({x:e,y:e}),M={left:"right",right:"left",bottom:"top",top:"bottom"},L={start:"end",end:"start"};function P(e,t){return"function"==typeof e?e(t):e}function N(e){return e.split("-")[0]}function B(e){return e.split("-")[1]}function O(e){return"x"===e?"y":"x"}function j(e){return"y"===e?"height":"width"}function q(e){return["top","bottom"].includes(N(e))?"y":"x"}function A(e){return e.replace(/start|end/g,e=>L[e])}function _(e){return e.replace(/left|right|bottom|top/g,e=>M[e])}function F(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function I(e,t,r){let n,{reference:o,floating:l}=e,i=q(t),a=O(q(t)),s=j(a),c=N(t),d="y"===i,u=o.x+o.width/2-l.width/2,f=o.y+o.height/2-l.height/2,p=o[s]/2-l[s]/2;switch(c){case"top":n={x:u,y:o.y-l.height};break;case"bottom":n={x:u,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-l.width,y:f};break;default:n={x:o.x,y:o.y}}switch(B(t)){case"start":n[a]-=p*(r&&d?-1:1);break;case"end":n[a]+=p*(r&&d?-1:1)}return n}let D=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:l=[],platform:i}=r,a=l.filter(Boolean),s=await (null==i.isRTL?void 0:i.isRTL(t)),c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:u}=I(c,n,s),f=n,p={},m=0;for(let r=0;r<a.length;r++){let{name:l,fn:g}=a[r],{x:b,y:h,data:v,reset:x}=await g({x:d,y:u,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:c,platform:i,elements:{reference:e,floating:t}});d=null!=b?b:d,u=null!=h?h:u,p={...p,[l]:{...p[l],...v}},x&&m<=50&&(m++,"object"==typeof x&&(x.placement&&(f=x.placement),x.rects&&(c=!0===x.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):x.rects),{x:d,y:u}=I(c,f,s)),r=-1)}return{x:d,y:u,placement:f,strategy:o,middlewareData:p}};async function V(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:l,rects:i,elements:a,strategy:s}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:f=!1,padding:p=0}=P(t,e),m="number"!=typeof p?{top:0,right:0,bottom:0,left:0,...p}:{top:p,right:p,bottom:p,left:p},g=a[f?"floating"===u?"reference":"floating":u],b=F(await l.getClippingRect({element:null==(r=await (null==l.isElement?void 0:l.isElement(g)))||r?g:g.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:c,rootBoundary:d,strategy:s})),h="floating"===u?{x:n,y:o,width:i.floating.width,height:i.floating.height}:i.reference,v=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),x=await (null==l.isElement?void 0:l.isElement(v))&&await (null==l.getScale?void 0:l.getScale(v))||{x:1,y:1},y=F(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:h,offsetParent:v,strategy:s}):h);return{top:(b.top-y.top+m.top)/x.y,bottom:(y.bottom-b.bottom+m.bottom)/x.y,left:(b.left-y.left+m.left)/x.x,right:(y.right-b.right+m.right)/x.x}}async function K(e,t){let{placement:r,platform:n,elements:o}=e,l=await (null==n.isRTL?void 0:n.isRTL(o.floating)),i=N(r),a=B(r),s="y"===q(r),c=["left","top"].includes(i)?-1:1,d=l&&s?-1:1,u=P(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return a&&"number"==typeof m&&(p="end"===a?-1*m:m),s?{x:p*d,y:f*c}:{x:f*c,y:p*d}}function X(e){let t=x(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=f(e),l=o?e.offsetWidth:r,i=o?e.offsetHeight:n,a=T(r)!==l||T(n)!==i;return a&&(r=l,n=i),{width:r,height:n,$:a}}function Y(e){return u(e)?e:e.contextElement}function Z(e){let t=Y(e);if(!f(t))return z(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:l}=X(t),i=(l?T(r.width):r.width)/n,a=(l?T(r.height):r.height)/o;return i&&Number.isFinite(i)||(i=1),a&&Number.isFinite(a)||(a=1),{x:i,y:a}}let H=z(0);function W(e){let t=s(e);return h()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:H}function G(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let l=e.getBoundingClientRect(),i=Y(e),a=z(1);t&&(n?u(n)&&(a=Z(n)):a=Z(e));let c=(void 0===(o=r)&&(o=!1),n&&(!o||n===s(i))&&o)?W(i):z(0),d=(l.left+c.x)/a.x,f=(l.top+c.y)/a.y,p=l.width/a.x,m=l.height/a.y;if(i){let e=s(i),t=n&&u(n)?s(n):n,r=e,o=C(r);for(;o&&n&&t!==r;){let e=Z(o),t=o.getBoundingClientRect(),n=x(o),l=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;d*=e.x,f*=e.y,p*=e.x,m*=e.y,d+=l,f+=i,o=C(r=s(o))}}return F({width:p,height:m,x:d,y:f})}function $(e,t){let r=y(e).scrollLeft;return t?t.left+r:G(c(e)).left+r}function J(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:$(e,n)),y:n.top+t.scrollTop}}function Q(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=s(e),n=c(e),o=r.visualViewport,l=n.clientWidth,i=n.clientHeight,a=0,d=0;if(o){l=o.width,i=o.height;let e=h();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,d=o.offsetTop)}return{width:l,height:i,x:a,y:d}}(e,r);else if("document"===t)n=function(e){let t=c(e),r=y(e),n=e.ownerDocument.body,o=R(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),l=R(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),i=-r.scrollLeft+$(e),a=-r.scrollTop;return"rtl"===x(n).direction&&(i+=R(t.clientWidth,n.clientWidth)-o),{width:o,height:l,x:i,y:a}}(c(e));else if(u(t))n=function(e,t){let r=G(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,l=f(e)?Z(e):z(1),i=e.clientWidth*l.x;return{width:i,height:e.clientHeight*l.y,x:o*l.x,y:n*l.y}}(t,r);else{let r=W(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return F(n)}function U(e){return"static"===x(e).position}function ee(e,t){if(!f(e)||"fixed"===x(e).position)return null;if(t)return t(e);let r=e.offsetParent;return c(e)===r&&(r=r.ownerDocument.body),r}function et(e,t){let r=s(e);if(g(e))return r;if(!f(e)){let t=w(e);for(;t&&!v(t);){if(u(t)&&!U(t))return t;t=w(t)}return r}let n=ee(e,t);for(;n&&["table","td","th"].includes(a(n))&&U(n);)n=ee(n,t);return n&&v(n)&&U(n)&&!b(n)?r:n||function(e){let t=w(e);for(;f(t)&&!v(t);){if(b(t))return t;if(g(t))break;t=w(t)}return null}(e)||r}let er=async function(e){let t=this.getOffsetParent||et,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=f(t),o=c(t),l="fixed"===r,i=G(e,!0,l,t),s={scrollLeft:0,scrollTop:0},d=z(0);if(n||!n&&!l){if(("body"!==a(t)||m(o))&&(s=y(t)),n){let e=G(t,!0,l,t);d.x=e.x+t.clientLeft,d.y=e.y+t.clientTop}else o&&(d.x=$(o))}l&&!n&&o&&(d.x=$(o));let u=!o||n||l?z(0):J(o,s);return{x:i.left+s.scrollLeft-d.x-u.x,y:i.top+s.scrollTop-d.y-u.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},en={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,l="fixed"===o,i=c(n),s=!!t&&g(t.floating);if(n===i||s&&l)return r;let d={scrollLeft:0,scrollTop:0},u=z(1),p=z(0),b=f(n);if((b||!b&&!l)&&(("body"!==a(n)||m(i))&&(d=y(n)),f(n))){let e=G(n);u=Z(n),p.x=e.x+n.clientLeft,p.y=e.y+n.clientTop}let h=!i||b||l?z(0):J(i,d,!0);return{width:r.width*u.x,height:r.height*u.y,x:r.x*u.x-d.scrollLeft*u.x+p.x+h.x,y:r.y*u.y-d.scrollTop*u.y+p.y+h.y}},getDocumentElement:c,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,l=[..."clippingAncestors"===r?g(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=k(e,[],!1).filter(e=>u(e)&&"body"!==a(e)),o=null,l="fixed"===x(e).position,i=l?w(e):e;for(;u(i)&&!v(i);){let t=x(i),r=b(i);r||"fixed"!==t.position||(o=null),(l?!r&&!o:!r&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||m(i)&&!r&&function e(t,r){let n=w(t);return!(n===r||!u(n)||v(n))&&("fixed"===x(n).position||e(n,r))}(e,i))?n=n.filter(e=>e!==i):o=t,i=w(i)}return t.set(e,n),n}(t,this._c):[].concat(r),n],i=l[0],s=l.reduce((e,r)=>{let n=Q(t,r,o);return e.top=R(n.top,e.top),e.right=E(n.right,e.right),e.bottom=E(n.bottom,e.bottom),e.left=R(n.left,e.left),e},Q(t,i,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:et,getElementRects:er,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=X(e);return{width:t,height:r}},getScale:Z,isElement:u,isRTL:function(e){return"rtl"===x(e).direction}};function eo(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function el(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:l=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=n,u=Y(e),f=l||i?[...u?k(u):[],...k(t)]:[];f.forEach(e=>{l&&e.addEventListener("scroll",r,{passive:!0}),i&&e.addEventListener("resize",r)});let p=u&&s?function(e,t){let r,n=null,o=c(e);function l(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function i(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),l();let c=e.getBoundingClientRect(),{left:d,top:u,width:f,height:p}=c;if(a||t(),!f||!p)return;let m=S(u),g=S(o.clientWidth-(d+f)),b={rootMargin:-m+"px "+-g+"px "+-S(o.clientHeight-(u+p))+"px "+-S(d)+"px",threshold:R(0,E(1,s))||1},h=!0;function v(t){let n=t[0].intersectionRatio;if(n!==s){if(!h)return i();n?i(!1,n):r=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==n||eo(c,e.getBoundingClientRect())||i(),h=!1}try{n=new IntersectionObserver(v,{...b,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(v,b)}n.observe(e)}(!0),l}(u,r):null,m=-1,g=null;a&&(g=new ResizeObserver(e=>{let[n]=e;n&&n.target===u&&g&&(g.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),r()}),u&&!d&&g.observe(u),g.observe(t));let b=d?G(e):null;return d&&function t(){let n=G(e);b&&!eo(b,n)&&r(),b=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;f.forEach(e=>{l&&e.removeEventListener("scroll",r),i&&e.removeEventListener("resize",r)}),null==p||p(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(o)}}let ei=(e,t,r)=>{let n=new Map,o={platform:en,...r},l={...o.platform,_c:n};return D(e,t,{...o,platform:l})};var ea="undefined"!=typeof document?n.useLayoutEffect:n.useEffect;function es(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!=t.length)return!1;for(n=r;0!=n--;)if(!es(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!Object.prototype.hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!es(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function ec(e){let t=n.useRef(e);return ea(()=>{t.current=e}),t}var ed="undefined"!=typeof document?n.useLayoutEffect:n.useEffect;let eu=!1,ef=0,ep=()=>"floating-ui-"+ef++,em=o["useId".toString()]||function(){let[e,t]=n.useState(()=>eu?ep():void 0);return ed(()=>{null==e&&t(ep())},[]),n.useEffect(()=>{eu||(eu=!0)},[]),e},eg=n.createContext(null),eb=n.createContext(null),eh=()=>{var e;return(null==(e=n.useContext(eg))?void 0:e.id)||null},ev=()=>n.useContext(eb);function ex(e){return(null==e?void 0:e.ownerDocument)||document}function ey(e){return ex(e).defaultView||window}function ew(e){return!!e&&e instanceof ey(e).Element}function ek(e){return!!e&&e instanceof ey(e).HTMLElement}function eC(e,t){let r=["mouse","pen"];return t||r.push("",void 0),r.includes(e)}function eE(e){let t=(0,n.useRef)(e);return ed(()=>{t.current=e}),t}let eR="data-floating-ui-safe-polygon";function eT(e,t,r){return r&&!eC(r)?0:"number"==typeof e?e:null==e?void 0:e[t]}let eS=function(e,t){let{enabled:r=!0,delay:o=0,handleClose:l=null,mouseOnly:i=!1,restMs:a=0,move:s=!0}=void 0===t?{}:t,{open:c,onOpenChange:d,dataRef:u,events:f,elements:{domReference:p,floating:m},refs:g}=e,b=ev(),h=eh(),v=eE(l),x=eE(o),y=n.useRef(),w=n.useRef(),k=n.useRef(),C=n.useRef(),E=n.useRef(!0),R=n.useRef(!1),T=n.useRef(()=>{}),S=n.useCallback(()=>{var e;let t=null==(e=u.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t},[u]);n.useEffect(()=>{if(r)return f.on("dismiss",e),()=>{f.off("dismiss",e)};function e(){clearTimeout(w.current),clearTimeout(C.current),E.current=!0}},[r,f]),n.useEffect(()=>{if(!r||!v.current||!c)return;function e(){S()&&d(!1)}let t=ex(m).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}},[m,c,d,r,v,u,S]);let z=n.useCallback(function(e){void 0===e&&(e=!0);let t=eT(x.current,"close",y.current);t&&!k.current?(clearTimeout(w.current),w.current=setTimeout(()=>d(!1),t)):e&&(clearTimeout(w.current),d(!1))},[x,d]),M=n.useCallback(()=>{T.current(),k.current=void 0},[]),L=n.useCallback(()=>{if(R.current){let e=ex(g.floating.current).body;e.style.pointerEvents="",e.removeAttribute(eR),R.current=!1}},[g]);return n.useEffect(()=>{if(r&&ew(p))return c&&p.addEventListener("mouseleave",l),null==m||m.addEventListener("mouseleave",l),s&&p.addEventListener("mousemove",n,{once:!0}),p.addEventListener("mouseenter",n),p.addEventListener("mouseleave",o),()=>{c&&p.removeEventListener("mouseleave",l),null==m||m.removeEventListener("mouseleave",l),s&&p.removeEventListener("mousemove",n),p.removeEventListener("mouseenter",n),p.removeEventListener("mouseleave",o)};function t(){return!!u.current.openEvent&&["click","mousedown"].includes(u.current.openEvent.type)}function n(e){if(clearTimeout(w.current),E.current=!1,i&&!eC(y.current)||a>0&&0===eT(x.current,"open"))return;u.current.openEvent=e;let t=eT(x.current,"open",y.current);t?w.current=setTimeout(()=>{d(!0)},t):d(!0)}function o(r){if(t())return;T.current();let n=ex(m);if(clearTimeout(C.current),v.current){c||clearTimeout(w.current),k.current=v.current({...e,tree:b,x:r.clientX,y:r.clientY,onClose(){L(),M(),z()}});let t=k.current;n.addEventListener("mousemove",t),T.current=()=>{n.removeEventListener("mousemove",t)};return}z()}function l(r){t()||null==v.current||v.current({...e,tree:b,x:r.clientX,y:r.clientY,onClose(){L(),M(),z()}})(r)}},[p,m,r,e,i,a,s,z,M,L,d,c,b,x,v,u]),ed(()=>{var e,t,n;if(r&&c&&null!=(e=v.current)&&e.__options.blockPointerEvents&&S()){let e=ex(m).body;if(e.setAttribute(eR,""),e.style.pointerEvents="none",R.current=!0,ew(p)&&m){let e=null==b?void 0:null==(t=b.nodesRef.current.find(e=>e.id===h))?void 0:null==(n=t.context)?void 0:n.elements.floating;return e&&(e.style.pointerEvents=""),p.style.pointerEvents="auto",m.style.pointerEvents="auto",()=>{p.style.pointerEvents="",m.style.pointerEvents=""}}}},[r,c,h,m,p,b,v,u,S]),ed(()=>{c||(y.current=void 0,M(),L())},[c,M,L]),n.useEffect(()=>()=>{M(),clearTimeout(w.current),clearTimeout(C.current),L()},[r,M,L]),n.useMemo(()=>{if(!r)return{};function e(e){y.current=e.pointerType}return{reference:{onPointerDown:e,onPointerEnter:e,onMouseMove(){c||0===a||(clearTimeout(C.current),C.current=setTimeout(()=>{E.current||d(!0)},a))}},floating:{onMouseEnter(){clearTimeout(w.current)},onMouseLeave(){f.emit("dismiss",{type:"mouseLeave",data:{returnFocus:!1}}),z(!1)}}}},[f,r,a,c,d,z])};function ez(e,t){if(!e||!t)return!1;let r=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(r&&function(e){if("undefined"==typeof ShadowRoot)return!1;let t=ey(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}(r)){let r=t;do{if(r&&e===r)return!0;r=r.parentNode||r.host}while(r)}return!1}function eM(e,t){let r=e.filter(e=>{var r;return e.parentId===t&&(null==(r=e.context)?void 0:r.open)})||[],n=r;for(;n.length;)n=e.filter(e=>{var t;return null==(t=n)?void 0:t.some(t=>{var r;return e.parentId===t.id&&(null==(r=e.context)?void 0:r.open)})})||[],r=r.concat(n);return r}let eL=o["useInsertionEffect".toString()]||(e=>e());function eP(e){let t=n.useRef(()=>{});return eL(()=>{t.current=e}),n.useCallback(function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return null==t.current?void 0:t.current(...r)},[])}function eN(e,t){return null!=t&&("composedPath"in e?e.composedPath().includes(t):null!=e.target&&t.contains(e.target))}let eB={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},eO={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},ej=function(e){var t,r;return void 0===e&&(e=!0),{escapeKeyBubbles:"boolean"==typeof e?e:null==(t=e.escapeKey)||t,outsidePressBubbles:"boolean"==typeof e?e:null==(r=e.outsidePress)||r}},eq=function(e,t){let{open:r,onOpenChange:o,events:l,nodeId:i,elements:{reference:a,domReference:s,floating:c},dataRef:d}=e,{enabled:u=!0,escapeKey:f=!0,outsidePress:p=!0,outsidePressEvent:m="pointerdown",referencePress:g=!1,referencePressEvent:b="pointerdown",ancestorScroll:h=!1,bubbles:v=!0}=void 0===t?{}:t,x=ev(),y=null!=eh(),w=eP("function"==typeof p?p:()=>!1),C="function"==typeof p?w:p,E=n.useRef(!1),{escapeKeyBubbles:R,outsidePressBubbles:T}=ej(v);return n.useEffect(()=>{if(!r||!u)return;function e(e){if("Escape"===e.key){let e=x?eM(x.nodesRef.current,i):[];if(e.length>0){let t=!0;if(e.forEach(e=>{var r;if(null!=(r=e.context)&&r.open&&!e.context.dataRef.current.__escapeKeyBubbles){t=!1;return}}),!t)return}l.emit("dismiss",{type:"escapeKey",data:{returnFocus:{preventScroll:!1}}}),o(!1)}}function t(e){var t;let r=E.current;if(E.current=!1,r||"function"==typeof C&&!C(e))return;let n="composedPath"in e?e.composedPath()[0]:e.target;if(ek(n)&&c){let t=c.ownerDocument.defaultView||window,r=n.scrollWidth>n.clientWidth,o=n.scrollHeight>n.clientHeight,l=o&&e.offsetX>n.clientWidth;if(o&&"rtl"===t.getComputedStyle(n).direction&&(l=e.offsetX<=n.offsetWidth-n.clientWidth),l||r&&e.offsetY>n.clientHeight)return}let a=x&&eM(x.nodesRef.current,i).some(t=>{var r;return eN(e,null==(r=t.context)?void 0:r.elements.floating)});if(eN(e,c)||eN(e,s)||a)return;let d=x?eM(x.nodesRef.current,i):[];if(d.length>0){let e=!0;if(d.forEach(t=>{var r;if(null!=(r=t.context)&&r.open&&!t.context.dataRef.current.__outsidePressBubbles){e=!1;return}}),!e)return}l.emit("dismiss",{type:"outsidePress",data:{returnFocus:y?{preventScroll:!0}:function(e){if(0===e.mozInputSource&&e.isTrusted)return!0;let t=/Android/i;return(t.test(function(){let e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}())||t.test(function(){let e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(e=>{let{brand:t,version:r}=e;return t+"/"+r}).join(" "):navigator.userAgent}()))&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType}(e)||0===(t=e).width&&0===t.height||1===t.width&&1===t.height&&0===t.pressure&&0===t.detail&&"mouse"!==t.pointerType||t.width<1&&t.height<1&&0===t.pressure&&0===t.detail}}),o(!1)}function n(){o(!1)}d.current.__escapeKeyBubbles=R,d.current.__outsidePressBubbles=T;let p=ex(c);f&&p.addEventListener("keydown",e),C&&p.addEventListener(m,t);let g=[];return h&&(ew(s)&&(g=k(s)),ew(c)&&(g=g.concat(k(c))),!ew(a)&&a&&a.contextElement&&(g=g.concat(k(a.contextElement)))),(g=g.filter(e=>{var t;return e!==(null==(t=p.defaultView)?void 0:t.visualViewport)})).forEach(e=>{e.addEventListener("scroll",n,{passive:!0})}),()=>{f&&p.removeEventListener("keydown",e),C&&p.removeEventListener(m,t),g.forEach(e=>{e.removeEventListener("scroll",n)})}},[d,c,s,a,f,C,m,l,x,i,r,o,h,u,R,T,y]),n.useEffect(()=>{E.current=!1},[C,m]),n.useMemo(()=>u?{reference:{[eB[b]]:()=>{g&&(l.emit("dismiss",{type:"referencePress",data:{returnFocus:!1}}),o(!1))}},floating:{[eO[m]]:()=>{E.current=!0}}}:{},[u,l,g,m,b,o])},eA=function(e,t){let{open:r,onOpenChange:o,dataRef:l,events:i,refs:a,elements:{floating:s,domReference:c}}=e,{enabled:d=!0,keyboardOnly:u=!0}=void 0===t?{}:t,f=n.useRef(""),p=n.useRef(!1),m=n.useRef();return n.useEffect(()=>{if(!d)return;let e=ex(s).defaultView||window;function t(){!r&&ek(c)&&c===function(e){let t=e.activeElement;for(;(null==(r=t)?void 0:null==(n=r.shadowRoot)?void 0:n.activeElement)!=null;){var r,n;t=t.shadowRoot.activeElement}return t}(ex(c))&&(p.current=!0)}return e.addEventListener("blur",t),()=>{e.removeEventListener("blur",t)}},[s,c,r,d]),n.useEffect(()=>{if(d)return i.on("dismiss",e),()=>{i.off("dismiss",e)};function e(e){("referencePress"===e.type||"escapeKey"===e.type)&&(p.current=!0)}},[i,d]),n.useEffect(()=>()=>{clearTimeout(m.current)},[]),n.useMemo(()=>d?{reference:{onPointerDown(e){let{pointerType:t}=e;f.current=t,p.current=!!(t&&u)},onMouseLeave(){p.current=!1},onFocus(e){var t;p.current||"focus"===e.type&&(null==(t=l.current.openEvent)?void 0:t.type)==="mousedown"&&l.current.openEvent&&eN(l.current.openEvent,c)||(l.current.openEvent=e.nativeEvent,o(!0))},onBlur(e){p.current=!1;let t=e.relatedTarget,r=ew(t)&&t.hasAttribute("data-floating-ui-focus-guard")&&"outside"===t.getAttribute("data-type");m.current=setTimeout(()=>{ez(a.floating.current,t)||ez(c,t)||r||o(!1)})}}}:{},[d,u,c,a,l,o])},e_=function(e,t){let{open:r}=e,{enabled:o=!0,role:l="dialog"}=void 0===t?{}:t,i=em(),a=em();return n.useMemo(()=>{let e={id:i,role:l};return o?"tooltip"===l?{reference:{"aria-describedby":r?i:void 0},floating:e}:{reference:{"aria-expanded":r?"true":"false","aria-haspopup":"alertdialog"===l?"dialog":l,"aria-controls":r?i:void 0,..."listbox"===l&&{role:"combobox"},..."menu"===l&&{id:a}},floating:{...e,..."menu"===l&&{"aria-labelledby":a}}}:{}},[o,l,r,i,a])};function eF(e,t,r){let n=new Map;return{..."floating"===r&&{tabIndex:-1},...e,...t.map(e=>e?e[r]:null).concat(e).reduce((e,t)=>(t&&Object.entries(t).forEach(t=>{let[r,o]=t;if(0===r.indexOf("on")){if(n.has(r)||n.set(r,[]),"function"==typeof o){var l;null==(l=n.get(r))||l.push(o),e[r]=function(){for(var e,t=arguments.length,o=Array(t),l=0;l<t;l++)o[l]=arguments[l];null==(e=n.get(r))||e.forEach(e=>e(...o))}}}else e[r]=o}),e),{})}}let eI=function(e){void 0===e&&(e=[]);let t=e,r=n.useCallback(t=>eF(t,e,"reference"),t),o=n.useCallback(t=>eF(t,e,"floating"),t),l=n.useCallback(t=>eF(t,e,"item"),e.map(e=>null==e?void 0:e.item));return n.useMemo(()=>({getReferenceProps:r,getFloatingProps:o,getItemProps:l}),[r,o,l])};var eD=r(4157);let eV=e=>{var t,r;let[o,i]=(0,n.useState)(!1),[a,s]=(0,n.useState)(),{x:c,y:d,refs:u,strategy:f,context:p}=function(e){void 0===e&&(e={});let{open:t=!1,onOpenChange:r,nodeId:o}=e,i=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:o=[],platform:i,whileElementsMounted:a,open:s}=e,[c,d]=n.useState({x:null,y:null,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[u,f]=n.useState(o);es(u,o)||f(o);let p=n.useRef(null),m=n.useRef(null),g=n.useRef(c),b=ec(a),h=ec(i),[v,x]=n.useState(null),[y,w]=n.useState(null),k=n.useCallback(e=>{p.current!==e&&(p.current=e,x(e))},[]),C=n.useCallback(e=>{m.current!==e&&(m.current=e,w(e))},[]),E=n.useCallback(()=>{if(!p.current||!m.current)return;let e={placement:t,strategy:r,middleware:u};h.current&&(e.platform=h.current),ei(p.current,m.current,e).then(e=>{let t={...e,isPositioned:!0};R.current&&!es(g.current,t)&&(g.current=t,l.flushSync(()=>{d(t)}))})},[u,t,r,h]);ea(()=>{!1===s&&g.current.isPositioned&&(g.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let R=n.useRef(!1);ea(()=>(R.current=!0,()=>{R.current=!1}),[]),ea(()=>{if(v&&y){if(b.current)return b.current(v,y,E);E()}},[v,y,E,b]);let T=n.useMemo(()=>({reference:p,floating:m,setReference:k,setFloating:C}),[k,C]),S=n.useMemo(()=>({reference:v,floating:y}),[v,y]);return n.useMemo(()=>({...c,update:E,refs:T,elements:S,reference:k,floating:C}),[c,E,T,S,k,C])}(e),a=ev(),s=n.useRef(null),c=n.useRef({}),d=n.useState(()=>(function(){let e=new Map;return{emit(t,r){var n;null==(n=e.get(t))||n.forEach(e=>e(r))},on(t,r){e.set(t,[...e.get(t)||[],r])},off(t,r){e.set(t,(e.get(t)||[]).filter(e=>e!==r))}}})())[0],[u,f]=n.useState(null),p=n.useCallback(e=>{let t=ew(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;i.refs.setReference(t)},[i.refs]),m=n.useCallback(e=>{(ew(e)||null===e)&&(s.current=e,f(e)),(ew(i.refs.reference.current)||null===i.refs.reference.current||null!==e&&!ew(e))&&i.refs.setReference(e)},[i.refs]),g=n.useMemo(()=>({...i.refs,setReference:m,setPositionReference:p,domReference:s}),[i.refs,m,p]),b=n.useMemo(()=>({...i.elements,domReference:u}),[i.elements,u]),h=eP(r),v=n.useMemo(()=>({...i,refs:g,elements:b,dataRef:c,nodeId:o,events:d,open:t,onOpenChange:h}),[i,o,d,t,h,g,b]);return ed(()=>{let e=null==a?void 0:a.nodesRef.current.find(e=>e.id===o);e&&(e.context=v)}),n.useMemo(()=>({...i,context:v,refs:g,reference:m,positionReference:p}),[i,g,v,m,p])}({open:o,onOpenChange:t=>{t&&e?s(setTimeout(()=>{i(t)},e)):(clearTimeout(a),i(t))},placement:"top",whileElementsMounted:el,middleware:[{name:"offset",options:5,async fn(e){var t,r;let{x:n,y:o,placement:l,middlewareData:i}=e,a=await K(e,5);return l===(null==(t=i.offset)?void 0:t.placement)&&null!=(r=i.arrow)&&r.alignmentOffset?{}:{x:n+a.x,y:o+a.y,data:{...a,placement:l}}}},{name:"flip",options:t={fallbackAxisSideDirection:"start"},async fn(e){var r,n,o,l,i,a;let{placement:s,middlewareData:c,rects:d,initialPlacement:u,platform:f,elements:p}=e,{mainAxis:m=!0,crossAxis:g=!0,fallbackPlacements:b,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:x=!0,...y}=P(t,e);if(null!=(r=c.arrow)&&r.alignmentOffset)return{};let w=N(s),k=q(u),C=N(u)===u,E=await (null==f.isRTL?void 0:f.isRTL(p.floating)),R=b||(C||!x?[_(u)]:function(e){let t=_(e);return[A(e),t,A(t)]}(u)),T="none"!==v;!b&&T&&R.push(...function(e,t,r,n){let o=B(e),l=function(e,t,r){let n=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(r)return t?o:n;return t?n:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(N(e),"start"===r,n);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(A)))),l}(u,x,v,E));let S=[u,...R],z=await V(e,y),M=[],L=(null==(n=c.flip)?void 0:n.overflows)||[];if(m&&M.push(z[w]),g){let e=function(e,t,r){void 0===r&&(r=!1);let n=B(e),o=O(q(e)),l=j(o),i="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=_(i)),[i,_(i)]}(s,d,E);M.push(z[e[0]],z[e[1]])}if(L=[...L,{placement:s,overflows:M}],!M.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=S[e];if(t){let r="alignment"===g&&k!==q(t),n=(null==(i=L[0])?void 0:i.overflows[0])>0;if(!r||n)return{data:{index:e,overflows:L},reset:{placement:t}}}let r=null==(l=L.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!r)switch(h){case"bestFit":{let e=null==(a=L.filter(e=>{if(T){let t=q(e.placement);return t===k||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(r=e);break}case"initialPlacement":r=u}if(s!==r)return{reset:{placement:r}}}return{}}},(void 0===r&&(r={}),{name:"shift",options:r,async fn(e){let{x:t,y:n,placement:o}=e,{mainAxis:l=!0,crossAxis:i=!1,limiter:a={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...s}=P(r,e),c={x:t,y:n},d=await V(e,s),u=q(N(o)),f=O(u),p=c[f],m=c[u];if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=p+d[e],n=p-d[t];p=R(r,E(p,n))}if(i){let e="y"===u?"top":"left",t="y"===u?"bottom":"right",r=m+d[e],n=m-d[t];m=R(r,E(m,n))}let g=a.fn({...e,[f]:p,[u]:m});return{...g,data:{x:g.x-t,y:g.y-n,enabled:{[f]:l,[u]:i}}}}})]}),m=eS(p,{move:!1}),{getReferenceProps:g,getFloatingProps:b}=eI([m,eA(p),eq(p),e_(p,{role:"tooltip"})]);return{tooltipProps:{open:o,x:c,y:d,refs:u,strategy:f,getFloatingProps:b},getReferenceProps:g}},eK=e=>{let{text:t,open:r,x:o,y:l,refs:i,strategy:a,getFloatingProps:s}=e;return r&&t?n.createElement("div",Object.assign({className:(0,eD.q)("max-w-xs text-sm z-20 rounded-tremor-default opacity-100 px-2.5 py-1","text-white bg-tremor-background-emphasis","dark:text-tremor-content-emphasis dark:bg-white"),ref:i.setFloating,style:{position:a,top:null!=l?l:0,left:null!=o?o:0}},s()),t):null};eK.displayName="Tooltip"},7084:function(e,t,r){r.d(t,{fr:function(){return n},m:function(){return i},u8:function(){return o},zS:function(){return l}});let n={Slate:"slate",Gray:"gray",Zinc:"zinc",Neutral:"neutral",Stone:"stone",Red:"red",Orange:"orange",Amber:"amber",Yellow:"yellow",Lime:"lime",Green:"green",Emerald:"emerald",Teal:"teal",Cyan:"cyan",Sky:"sky",Blue:"blue",Indigo:"indigo",Violet:"violet",Purple:"purple",Fuchsia:"fuchsia",Pink:"pink",Rose:"rose"},o={XS:"xs",SM:"sm",MD:"md",LG:"lg",XL:"xl"},l={Left:"left",Right:"right"},i={Top:"top",Bottom:"bottom"}},6898:function(e,t,r){r.d(t,{K:function(){return o}});var n=r(7084);let o={canvasBackground:50,lightBackground:100,background:500,darkBackground:600,darkestBackground:800,lightBorder:200,border:500,darkBorder:700,lightRing:200,ring:300,iconRing:500,lightText:400,text:500,iconText:600,darkText:700,darkestText:900,icon:500};n.fr.Blue,n.fr.Cyan,n.fr.Sky,n.fr.Indigo,n.fr.Violet,n.fr.Purple,n.fr.Fuchsia,n.fr.Slate,n.fr.Gray,n.fr.Zinc,n.fr.Neutral,n.fr.Stone,n.fr.Red,n.fr.Orange,n.fr.Amber,n.fr.Yellow,n.fr.Lime,n.fr.Green,n.fr.Emerald,n.fr.Teal,n.fr.Pink,n.fr.Rose},4157:function(e,t,r){r.d(t,{q:function(){return et}});let n=e=>{let t=a(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||i(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),l=n?o(e.slice(1),n):void 0;if(l)return l;if(0===t.validators.length)return;let i=e.join("-");return t.validators.find(({validator:e})=>e(i))?.classGroupId},l=/^\[(.+)\]$/,i=e=>{if(l.test(e)){let t=l.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},a=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return u(Object.entries(e.classGroups),r).forEach(([e,r])=>{s(r,n,e,t)}),n},s=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:c(t,e)).classGroupId=r;return}if("function"==typeof e){if(d(e)){s(e(n),t,r,n);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,o])=>{s(o,c(t,e),r,n)})})},c=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},d=e=>e.isThemeGetter,u=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,l)=>{r.set(o,l),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},p=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,o=t[0],l=t.length,i=e=>{let r;let i=[],a=0,s=0;for(let c=0;c<e.length;c++){let d=e[c];if(0===a){if(d===o&&(n||e.slice(c,c+l)===t)){i.push(e.slice(s,c)),s=c+l;continue}if("/"===d){r=c;continue}}"["===d?a++:"]"===d&&a--}let c=0===i.length?e:e.substring(s),d=c.startsWith("!"),u=d?c.substring(1):c;return{modifiers:i,hasImportantModifier:d,baseClassName:u,maybePostfixModifierPosition:r&&r>s?r-s:void 0}};return r?e=>r({className:e,parseClassName:i}):i},m=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},g=e=>({cache:f(e.cacheSize),parseClassName:p(e),...n(e)}),b=/\s+/,h=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o}=t,l=[],i=e.trim().split(b),a="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{modifiers:s,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:u}=r(t),f=!!u,p=n(f?d.substring(0,u):d);if(!p){if(!f||!(p=n(d))){a=t+(a.length>0?" "+a:a);continue}f=!1}let g=m(s).join(":"),b=c?g+"!":g,h=b+p;if(l.includes(h))continue;l.push(h);let v=o(p,f);for(let e=0;e<v.length;++e){let t=v[e];l.push(b+t)}a=t+(a.length>0?" "+a:a)}return a};function v(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=x(e))&&(n&&(n+=" "),n+=t);return n}let x=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=x(e[n]))&&(r&&(r+=" "),r+=t);return r};function y(e,...t){let r,n,o;let l=function(a){return n=(r=g(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,l=i,i(a)};function i(e){let t=n(e);if(t)return t;let l=h(e,r);return o(e,l),l}return function(){return l(v.apply(null,arguments))}}let w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},k=/^\[(?:([a-z-]+):)?(.+)\]$/i,C=/^\d+\/\d+$/,E=new Set(["px","full","screen"]),R=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,T=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,z=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,M=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,L=e=>N(e)||E.has(e)||C.test(e),P=e=>Y(e,"length",Z),N=e=>!!e&&!Number.isNaN(Number(e)),B=e=>Y(e,"number",N),O=e=>!!e&&Number.isInteger(Number(e)),j=e=>e.endsWith("%")&&N(e.slice(0,-1)),q=e=>k.test(e),A=e=>R.test(e),_=new Set(["length","size","percentage"]),F=e=>Y(e,_,H),I=e=>Y(e,"position",H),D=new Set(["image","url"]),V=e=>Y(e,D,G),K=e=>Y(e,"",W),X=()=>!0,Y=(e,t,r)=>{let n=k.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},Z=e=>T.test(e)&&!S.test(e),H=()=>!1,W=e=>z.test(e),G=e=>M.test(e),$=()=>{let e=w("colors"),t=w("spacing"),r=w("blur"),n=w("brightness"),o=w("borderColor"),l=w("borderRadius"),i=w("borderSpacing"),a=w("borderWidth"),s=w("contrast"),c=w("grayscale"),d=w("hueRotate"),u=w("invert"),f=w("gap"),p=w("gradientColorStops"),m=w("gradientColorStopPositions"),g=w("inset"),b=w("margin"),h=w("opacity"),v=w("padding"),x=w("saturate"),y=w("scale"),k=w("sepia"),C=w("skew"),E=w("space"),R=w("translate"),T=()=>["auto","contain","none"],S=()=>["auto","hidden","clip","visible","scroll"],z=()=>["auto",q,t],M=()=>[q,t],_=()=>["",L,P],D=()=>["auto",N,q],Y=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Z=()=>["solid","dashed","dotted","double","none"],H=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],W=()=>["start","end","center","between","around","evenly","stretch"],G=()=>["","0",q],$=()=>["auto","avoid","all","avoid-page","page","left","right","column"],J=()=>[N,q];return{cacheSize:500,separator:":",theme:{colors:[X],spacing:[L,P],blur:["none","",A,q],brightness:J(),borderColor:[e],borderRadius:["none","","full",A,q],borderSpacing:M(),borderWidth:_(),contrast:J(),grayscale:G(),hueRotate:J(),invert:G(),gap:M(),gradientColorStops:[e],gradientColorStopPositions:[j,P],inset:z(),margin:z(),opacity:J(),padding:M(),saturate:J(),scale:J(),sepia:G(),skew:J(),space:M(),translate:M()},classGroups:{aspect:[{aspect:["auto","square","video",q]}],container:["container"],columns:[{columns:[A]}],"break-after":[{"break-after":$()}],"break-before":[{"break-before":$()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Y(),q]}],overflow:[{overflow:S()}],"overflow-x":[{"overflow-x":S()}],"overflow-y":[{"overflow-y":S()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[g]}],"inset-x":[{"inset-x":[g]}],"inset-y":[{"inset-y":[g]}],start:[{start:[g]}],end:[{end:[g]}],top:[{top:[g]}],right:[{right:[g]}],bottom:[{bottom:[g]}],left:[{left:[g]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",O,q]}],basis:[{basis:z()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",q]}],grow:[{grow:G()}],shrink:[{shrink:G()}],order:[{order:["first","last","none",O,q]}],"grid-cols":[{"grid-cols":[X]}],"col-start-end":[{col:["auto",{span:["full",O,q]},q]}],"col-start":[{"col-start":D()}],"col-end":[{"col-end":D()}],"grid-rows":[{"grid-rows":[X]}],"row-start-end":[{row:["auto",{span:[O,q]},q]}],"row-start":[{"row-start":D()}],"row-end":[{"row-end":D()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",q]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",q]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...W()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...W(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...W(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[E]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[E]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",q,t]}],"min-w":[{"min-w":[q,t,"min","max","fit"]}],"max-w":[{"max-w":[q,t,"none","full","min","max","fit","prose",{screen:[A]},A]}],h:[{h:[q,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[q,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[q,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[q,t,"auto","min","max","fit"]}],"font-size":[{text:["base",A,P]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",B]}],"font-family":[{font:[X]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",q]}],"line-clamp":[{"line-clamp":["none",N,B]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",L,q]}],"list-image":[{"list-image":["none",q]}],"list-style-type":[{list:["none","disc","decimal",q]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Z(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",L,P]}],"underline-offset":[{"underline-offset":["auto",L,q]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:M()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",q]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",q]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Y(),I]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",F]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},V]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...Z(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:Z()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...Z()]}],"outline-offset":[{"outline-offset":[L,q]}],"outline-w":[{outline:[L,P]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:_()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[L,P]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",A,K]}],"shadow-color":[{shadow:[X]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":[...H(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":H()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",A,q]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[u]}],saturate:[{saturate:[x]}],sepia:[{sepia:[k]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[x]}],"backdrop-sepia":[{"backdrop-sepia":[k]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",q]}],duration:[{duration:J()}],ease:[{ease:["linear","in","out","in-out",q]}],delay:[{delay:J()}],animate:[{animate:["none","spin","ping","pulse","bounce",q]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[y]}],"scale-x":[{"scale-x":[y]}],"scale-y":[{"scale-y":[y]}],rotate:[{rotate:[O,q]}],"translate-x":[{"translate-x":[R]}],"translate-y":[{"translate-y":[R]}],"skew-x":[{"skew-x":[C]}],"skew-y":[{"skew-y":[C]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",q]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",q]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":M()}],"scroll-mx":[{"scroll-mx":M()}],"scroll-my":[{"scroll-my":M()}],"scroll-ms":[{"scroll-ms":M()}],"scroll-me":[{"scroll-me":M()}],"scroll-mt":[{"scroll-mt":M()}],"scroll-mr":[{"scroll-mr":M()}],"scroll-mb":[{"scroll-mb":M()}],"scroll-ml":[{"scroll-ml":M()}],"scroll-p":[{"scroll-p":M()}],"scroll-px":[{"scroll-px":M()}],"scroll-py":[{"scroll-py":M()}],"scroll-ps":[{"scroll-ps":M()}],"scroll-pe":[{"scroll-pe":M()}],"scroll-pt":[{"scroll-pt":M()}],"scroll-pr":[{"scroll-pr":M()}],"scroll-pb":[{"scroll-pb":M()}],"scroll-pl":[{"scroll-pl":M()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",q]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[L,P,B]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},J=(e,{cacheSize:t,prefix:r,separator:n,experimentalParseClassName:o,extend:l={},override:i={}})=>{for(let l in Q(e,"cacheSize",t),Q(e,"prefix",r),Q(e,"separator",n),Q(e,"experimentalParseClassName",o),i)U(e[l],i[l]);for(let t in l)ee(e[t],l[t]);return e},Q=(e,t,r)=>{void 0!==r&&(e[t]=r)},U=(e,t)=>{if(t)for(let r in t)Q(e,r,t[r])},ee=(e,t)=>{if(t)for(let r in t){let n=t[r];void 0!==n&&(e[r]=(e[r]||[]).concat(n))}},et=((e,...t)=>"function"==typeof e?y($,e,...t):y(()=>J($(),e),...t))({extend:{classGroups:{shadow:[{shadow:[{tremor:["input","card","dropdown"],"dark-tremor":["input","card","dropdown"]}]}],rounded:[{rounded:[{tremor:["small","default","full"],"dark-tremor":["small","default","full"]}]}],"font-size":[{text:[{tremor:["default","title","metric"],"dark-tremor":["default","title","metric"]}]}]}}})},1153:function(e,t,r){r.d(t,{bM:function(){return a},fn:function(){return i},lq:function(){return l}}),r(7084);let n=["slate","gray","zinc","neutral","stone","red","orange","amber","yellow","lime","green","emerald","teal","cyan","sky","blue","indigo","violet","purple","fuchsia","pink","rose"],o=e=>n.includes(e);function l(e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}}function i(e){return t=>"tremor-".concat(e,"-").concat(t)}function a(e,t){let r=o(e);if("white"===e||"black"===e||"transparent"===e||!t||!r){let t=e.includes("#")||e.includes("--")||e.includes("rgb")?"[".concat(e,"]"):e;return{bgColor:"bg-".concat(t," dark:bg-").concat(t),hoverBgColor:"hover:bg-".concat(t," dark:hover:bg-").concat(t),selectBgColor:"data-[selected]:bg-".concat(t," dark:data-[selected]:bg-").concat(t),textColor:"text-".concat(t," dark:text-").concat(t),selectTextColor:"data-[selected]:text-".concat(t," dark:data-[selected]:text-").concat(t),hoverTextColor:"hover:text-".concat(t," dark:hover:text-").concat(t),borderColor:"border-".concat(t," dark:border-").concat(t),selectBorderColor:"data-[selected]:border-".concat(t," dark:data-[selected]:border-").concat(t),hoverBorderColor:"hover:border-".concat(t," dark:hover:border-").concat(t),ringColor:"ring-".concat(t," dark:ring-").concat(t),strokeColor:"stroke-".concat(t," dark:stroke-").concat(t),fillColor:"fill-".concat(t," dark:fill-").concat(t)}}return{bgColor:"bg-".concat(e,"-").concat(t," dark:bg-").concat(e,"-").concat(t),selectBgColor:"data-[selected]:bg-".concat(e,"-").concat(t," dark:data-[selected]:bg-").concat(e,"-").concat(t),hoverBgColor:"hover:bg-".concat(e,"-").concat(t," dark:hover:bg-").concat(e,"-").concat(t),textColor:"text-".concat(e,"-").concat(t," dark:text-").concat(e,"-").concat(t),selectTextColor:"data-[selected]:text-".concat(e,"-").concat(t," dark:data-[selected]:text-").concat(e,"-").concat(t),hoverTextColor:"hover:text-".concat(e,"-").concat(t," dark:hover:text-").concat(e,"-").concat(t),borderColor:"border-".concat(e,"-").concat(t," dark:border-").concat(e,"-").concat(t),selectBorderColor:"data-[selected]:border-".concat(e,"-").concat(t," dark:data-[selected]:border-").concat(e,"-").concat(t),hoverBorderColor:"hover:border-".concat(e,"-").concat(t," dark:hover:border-").concat(e,"-").concat(t),ringColor:"ring-".concat(e,"-").concat(t," dark:ring-").concat(e,"-").concat(t),strokeColor:"stroke-".concat(e,"-").concat(t," dark:stroke-").concat(e,"-").concat(t),fillColor:"fill-".concat(e,"-").concat(t," dark:fill-").concat(e,"-").concat(t)}}},6200:function(e,t,r){function n(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}r.d(t,{_T:function(){return n}}),"function"==typeof SuppressedError&&SuppressedError}}]);