/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/digital/p25/enrich/dashboard/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGamVhbi1wYXRyaWNrc21pdGglMkZkaWdpdGFsJTJGcDI1JTJGZW5yaWNoJTJGZGFzaGJvYXJkJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUF1RyIsInNvdXJjZXMiOlsid2VicGFjazovL3AyNS1kYXNoYm9hcmQvPzA2OGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvamVhbi1wYXRyaWNrc21pdGgvZGlnaXRhbC9wMjUvZW5yaWNoL2Rhc2hib2FyZC9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Metric,Text,TextInput,Title!=!@tremor/react */ \"(ssr)/__barrel_optimize__?names=Badge,Button,Card,Metric,Text,TextInput,Title!=!./node_modules/@tremor/react/dist/index.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Dashboard() {\n    const [policies, setPolicies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedPolicy, setSelectedPolicy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchPolicies = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/policies\");\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch policies\");\n                }\n                const data = await response.json();\n                setPolicies(data);\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"An error occurred\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchPolicies();\n    }, []);\n    const filteredPolicies = policies.filter((policy)=>policy.short_title.toLowerCase().includes(searchTerm.toLowerCase()) || policy.description.toLowerCase().includes(searchTerm.toLowerCase()) || policy.policy_id.toString().includes(searchTerm));\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        children: \"Loading policies...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"Error: \",\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this);\n    }\n    if (selectedPolicy) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setSelectedPolicy(null),\n                            children: \"← Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                            color: \"blue\",\n                                            children: [\n                                                \"Policy #\",\n                                                selectedPolicy.policy_id\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Title, {\n                                            className: \"text-2xl mt-2\",\n                                            children: selectedPolicy.short_title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                    children: \"Perplexity Length\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Metric, {\n                                                    children: selectedPolicy.source_analysis.perplexity_content_length\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                    children: \"RAG Length\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Metric, {\n                                                    children: selectedPolicy.source_analysis.rag_content_length\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                    children: \"Citations\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Metric, {\n                                                    children: selectedPolicy.source_analysis.citations_found\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            className: \"font-semibold\",\n                                            children: \"Description:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            className: \"mt-2\",\n                                            children: selectedPolicy.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            className: \"font-semibold\",\n                                            children: \"Original Title:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            className: \"mt-2 text-sm text-gray-600\",\n                                            children: selectedPolicy.policy_title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            className: \"font-semibold\",\n                                            children: \"Perplexity Analysis:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 text-sm prose prose-sm max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                                children: selectedPolicy.source_analysis.perplexity_description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            className: \"font-semibold\",\n                                            children: \"RAG Context:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 text-sm prose prose-sm max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                                children: selectedPolicy.source_analysis.rag_description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Title, {\n                            className: \"text-3xl font-bold mb-2\",\n                            children: \"Project 2025 Policy Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                            className: \"text-gray-600\",\n                            children: \"Interactive dashboard for viewing and analyzing processed policies\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                    color: \"blue\",\n                                    children: [\n                                        policies.length,\n                                        \" Policies Processed\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                    color: \"emerald\",\n                                    children: \"Enhanced RAG v2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                    color: \"purple\",\n                                    children: \"Short Titles\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.TextInput, {\n                        placeholder: \"Search policies by ID, title, or description...\",\n                        value: searchTerm,\n                        onValueChange: setSearchTerm,\n                        className: \"max-w-md\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        children: [\n                            \"Showing \",\n                            filteredPolicies.length,\n                            \" of \",\n                            policies.length,\n                            \" policies\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                    children: filteredPolicies.map((policy)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"cursor-pointer hover:shadow-lg transition-shadow\",\n                            onClick: ()=>setSelectedPolicy(policy),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                color: \"blue\",\n                                                children: [\n                                                    \"#\",\n                                                    policy.policy_id\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                className: \"text-xs text-gray-500\",\n                                                children: new Date(policy.processing_metadata.processing_timestamp).toLocaleDateString()\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Title, {\n                                        className: \"text-lg\",\n                                        children: policy.short_title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                        className: \"text-sm text-gray-600 line-clamp-2\",\n                                        children: policy.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-2 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Metric, {\n                                                        className: \"text-sm\",\n                                                        children: policy.source_analysis.perplexity_content_length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                        className: \"text-xs\",\n                                                        children: \"Perplexity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Metric, {\n                                                        className: \"text-sm\",\n                                                        children: policy.source_analysis.rag_content_length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                        className: \"text-xs\",\n                                                        children: \"RAG\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Metric, {\n                                                        className: \"text-sm\",\n                                                        children: policy.source_analysis.citations_found\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                        className: \"text-xs\",\n                                                        children: \"Citations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, this)\n                        }, policy.policy_id, false, {\n                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this),\n                filteredPolicies.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        className: \"text-gray-500\",\n                        children: \"No policies found matching your search criteria.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=Badge,Button,Card,Metric,Text,TextInput,Title!=!./node_modules/@tremor/react/dist/index.js":
/*!****************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Badge,Button,Card,Metric,Text,TextInput,Title!=!./node_modules/@tremor/react/dist/index.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* reexport safe */ _components_icon_elements_Badge_Badge_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Button: () => (/* reexport safe */ _components_input_elements_Button_Button_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Card: () => (/* reexport safe */ _components_layout_elements_Card_Card_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Metric: () => (/* reexport safe */ _components_text_elements_Metric_Metric_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Text: () => (/* reexport safe */ _components_text_elements_Text_Text_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   TextInput: () => (/* reexport safe */ _components_input_elements_TextInput_TextInput_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   Title: () => (/* reexport safe */ _components_text_elements_Title_Title_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _components_icon_elements_Badge_Badge_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components/icon-elements/Badge/Badge.js */ \"(ssr)/./node_modules/@tremor/react/dist/components/icon-elements/Badge/Badge.js\");\n/* harmony import */ var _components_input_elements_Button_Button_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/input-elements/Button/Button.js */ \"(ssr)/./node_modules/@tremor/react/dist/components/input-elements/Button/Button.js\");\n/* harmony import */ var _components_layout_elements_Card_Card_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/layout-elements/Card/Card.js */ \"(ssr)/./node_modules/@tremor/react/dist/components/layout-elements/Card/Card.js\");\n/* harmony import */ var _components_text_elements_Metric_Metric_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/text-elements/Metric/Metric.js */ \"(ssr)/./node_modules/@tremor/react/dist/components/text-elements/Metric/Metric.js\");\n/* harmony import */ var _components_text_elements_Text_Text_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/text-elements/Text/Text.js */ \"(ssr)/./node_modules/@tremor/react/dist/components/text-elements/Text/Text.js\");\n/* harmony import */ var _components_input_elements_TextInput_TextInput_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/input-elements/TextInput/TextInput.js */ \"(ssr)/./node_modules/@tremor/react/dist/components/input-elements/TextInput/TextInput.js\");\n/* harmony import */ var _components_text_elements_Title_Title_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/text-elements/Title/Title.js */ \"(ssr)/./node_modules/@tremor/react/dist/components/text-elements/Title/Title.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYWRnZSxCdXR0b24sQ2FyZCxNZXRyaWMsVGV4dCxUZXh0SW5wdXQsVGl0bGUhPSEuL25vZGVfbW9kdWxlcy9AdHJlbW9yL3JlYWN0L2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNEU7QUFDSTtBQUNMO0FBQ0k7QUFDTjtBQUNnQjtBQUNiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcDI1LWRhc2hib2FyZC8uL25vZGVfbW9kdWxlcy9AdHJlbW9yL3JlYWN0L2Rpc3QvaW5kZXguanM/OTcwYyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQmFkZ2UgfSBmcm9tIFwiLi9jb21wb25lbnRzL2ljb24tZWxlbWVudHMvQmFkZ2UvQmFkZ2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCdXR0b24gfSBmcm9tIFwiLi9jb21wb25lbnRzL2lucHV0LWVsZW1lbnRzL0J1dHRvbi9CdXR0b24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYXJkIH0gZnJvbSBcIi4vY29tcG9uZW50cy9sYXlvdXQtZWxlbWVudHMvQ2FyZC9DYXJkLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWV0cmljIH0gZnJvbSBcIi4vY29tcG9uZW50cy90ZXh0LWVsZW1lbnRzL01ldHJpYy9NZXRyaWMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUZXh0IH0gZnJvbSBcIi4vY29tcG9uZW50cy90ZXh0LWVsZW1lbnRzL1RleHQvVGV4dC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRleHRJbnB1dCB9IGZyb20gXCIuL2NvbXBvbmVudHMvaW5wdXQtZWxlbWVudHMvVGV4dElucHV0L1RleHRJbnB1dC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRpdGxlIH0gZnJvbSBcIi4vY29tcG9uZW50cy90ZXh0LWVsZW1lbnRzL1RpdGxlL1RpdGxlLmpzXCIiXSwibmFtZXMiOlsiZGVmYXVsdCIsIkJhZGdlIiwiQnV0dG9uIiwiQ2FyZCIsIk1ldHJpYyIsIlRleHQiLCJUZXh0SW5wdXQiLCJUaXRsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=Badge,Button,Card,Metric,Text,TextInput,Title!=!./node_modules/@tremor/react/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"205a5349122c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wMjUtZGFzaGJvYXJkLy4vYXBwL2dsb2JhbHMuY3NzPzZkNDUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyMDVhNTM0OTEyMmNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"Project 2025 Policy Dashboard\",\n    description: \"Interactive dashboard for Project 2025 policy analysis\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDc0I7QUFFZixNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFBTUg7Ozs7Ozs7Ozs7O0FBR2IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wMjUtZGFzaGJvYXJkLy4vYXBwL2xheW91dC50c3g/OTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdQcm9qZWN0IDIwMjUgUG9saWN5IERhc2hib2FyZCcsXG4gIGRlc2NyaXB0aW9uOiAnSW50ZXJhY3RpdmUgZGFzaGJvYXJkIGZvciBQcm9qZWN0IDIwMjUgcG9saWN5IGFuYWx5c2lzJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keT57Y2hpbGRyZW59PC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufSJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/@tremor","vendor-chunks/property-information","vendor-chunks/micromark","vendor-chunks/@floating-ui","vendor-chunks/micromark-util-symbol","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/react-transition-state","vendor-chunks/micromark-util-subtokenize","vendor-chunks/style-to-js","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/tslib","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/tailwind-merge","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-from-markdown","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/character-entities","vendor-chunks/bail","vendor-chunks/tabbable","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend","vendor-chunks/aria-hidden"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();