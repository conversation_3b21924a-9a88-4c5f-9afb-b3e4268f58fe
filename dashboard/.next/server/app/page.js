(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4776:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>f,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),r(908),r(1506),r(5866);var n=r(3191),o=r(8716),l=r(7922),i=r.n(l),a=r(5231),s={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>a[e]);r.d(t,s);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,908)),"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,1506)),"/Users/<USER>/digital/p25/enrich/dashboard/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx"],u="/page",f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1222:()=>{},9348:(e,t,r)=>{Promise.resolve().then(r.bind(r,2756))},4775:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},2756:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>rp});var n=r(326),o=r(7577),l=r.n(o);function i(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create,Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,r(962));function s(){return"undefined"!=typeof window}function c(e){return f(e)?(e.nodeName||"").toLowerCase():"#document"}function d(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function u(e){var t;return null==(t=(f(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function f(e){return!!s()&&(e instanceof Node||e instanceof d(e).Node)}function p(e){return!!s()&&(e instanceof Element||e instanceof d(e).Element)}function m(e){return!!s()&&(e instanceof HTMLElement||e instanceof d(e).HTMLElement)}function g(e){return!!s()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof d(e).ShadowRoot)}function h(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=w(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function b(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function x(e){let t=v(),r=p(e)?w(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function v(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function y(e){return["html","body","#document"].includes(c(e))}function w(e){return d(e).getComputedStyle(e)}function k(e){return p(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function C(e){if("html"===c(e))return e;let t=e.assignedSlot||e.parentNode||g(e)&&e.host||u(e);return g(t)?t.host:t}function E(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=C(t);return y(r)?t.ownerDocument?t.ownerDocument.body:t.body:m(r)&&h(r)?r:e(r)}(e),l=o===(null==(n=e.ownerDocument)?void 0:n.body),i=d(o);if(l){let e=j(i);return t.concat(i,i.visualViewport||[],h(o)?o:[],e&&r?E(e):[])}return t.concat(o,E(o,[],r))}function j(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}let R=Math.min,N=Math.max,P=Math.round,S=Math.floor,T=e=>({x:e,y:e}),L={left:"right",right:"left",bottom:"top",top:"bottom"},$={start:"end",end:"start"};function z(e,t){return"function"==typeof e?e(t):e}function M(e){return e.split("-")[0]}function _(e){return e.split("-")[1]}function O(e){return"x"===e?"y":"x"}function B(e){return"y"===e?"height":"width"}function A(e){return["top","bottom"].includes(M(e))?"y":"x"}function D(e){return e.replace(/start|end/g,e=>$[e])}function I(e){return e.replace(/left|right|bottom|top/g,e=>L[e])}function F(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function V(e,t,r){let n,{reference:o,floating:l}=e,i=A(t),a=O(A(t)),s=B(a),c=M(t),d="y"===i,u=o.x+o.width/2-l.width/2,f=o.y+o.height/2-l.height/2,p=o[s]/2-l[s]/2;switch(c){case"top":n={x:u,y:o.y-l.height};break;case"bottom":n={x:u,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-l.width,y:f};break;default:n={x:o.x,y:o.y}}switch(_(t)){case"start":n[a]-=p*(r&&d?-1:1);break;case"end":n[a]+=p*(r&&d?-1:1)}return n}let G=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:l=[],platform:i}=r,a=l.filter(Boolean),s=await (null==i.isRTL?void 0:i.isRTL(t)),c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:u}=V(c,n,s),f=n,p={},m=0;for(let r=0;r<a.length;r++){let{name:l,fn:g}=a[r],{x:h,y:b,data:x,reset:v}=await g({x:d,y:u,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:c,platform:i,elements:{reference:e,floating:t}});d=null!=h?h:d,u=null!=b?b:u,p={...p,[l]:{...p[l],...x}},v&&m<=50&&(m++,"object"==typeof v&&(v.placement&&(f=v.placement),v.rects&&(c=!0===v.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):v.rects),{x:d,y:u}=V(c,f,s)),r=-1)}return{x:d,y:u,placement:f,strategy:o,middlewareData:p}};async function X(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:l,rects:i,elements:a,strategy:s}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:f=!1,padding:p=0}=z(t,e),m="number"!=typeof p?{top:0,right:0,bottom:0,left:0,...p}:{top:p,right:p,bottom:p,left:p},g=a[f?"floating"===u?"reference":"floating":u],h=F(await l.getClippingRect({element:null==(r=await (null==l.isElement?void 0:l.isElement(g)))||r?g:g.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:c,rootBoundary:d,strategy:s})),b="floating"===u?{x:n,y:o,width:i.floating.width,height:i.floating.height}:i.reference,x=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),v=await (null==l.isElement?void 0:l.isElement(x))&&await (null==l.getScale?void 0:l.getScale(x))||{x:1,y:1},y=F(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:b,offsetParent:x,strategy:s}):b);return{top:(h.top-y.top+m.top)/v.y,bottom:(y.bottom-h.bottom+m.bottom)/v.y,left:(h.left-y.left+m.left)/v.x,right:(y.right-h.right+m.right)/v.x}}async function Y(e,t){let{placement:r,platform:n,elements:o}=e,l=await (null==n.isRTL?void 0:n.isRTL(o.floating)),i=M(r),a=_(r),s="y"===A(r),c=["left","top"].includes(i)?-1:1,d=l&&s?-1:1,u=z(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return a&&"number"==typeof m&&(p="end"===a?-1*m:m),s?{x:p*d,y:f*c}:{x:f*c,y:p*d}}function H(e){let t=w(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=m(e),l=o?e.offsetWidth:r,i=o?e.offsetHeight:n,a=P(r)!==l||P(n)!==i;return a&&(r=l,n=i),{width:r,height:n,$:a}}function W(e){return p(e)?e:e.contextElement}function q(e){let t=W(e);if(!m(t))return T(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:l}=H(t),i=(l?P(r.width):r.width)/n,a=(l?P(r.height):r.height)/o;return i&&Number.isFinite(i)||(i=1),a&&Number.isFinite(a)||(a=1),{x:i,y:a}}let Z=T(0);function K(e){let t=d(e);return v()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Z}function U(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let l=e.getBoundingClientRect(),i=W(e),a=T(1);t&&(n?p(n)&&(a=q(n)):a=q(e));let s=(void 0===(o=r)&&(o=!1),n&&(!o||n===d(i))&&o)?K(i):T(0),c=(l.left+s.x)/a.x,u=(l.top+s.y)/a.y,f=l.width/a.x,m=l.height/a.y;if(i){let e=d(i),t=n&&p(n)?d(n):n,r=e,o=j(r);for(;o&&n&&t!==r;){let e=q(o),t=o.getBoundingClientRect(),n=w(o),l=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,u*=e.y,f*=e.x,m*=e.y,c+=l,u+=i,o=j(r=d(o))}}return F({width:f,height:m,x:c,y:u})}function J(e,t){let r=k(e).scrollLeft;return t?t.left+r:U(u(e)).left+r}function Q(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:J(e,n)),y:n.top+t.scrollTop}}function ee(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=d(e),n=u(e),o=r.visualViewport,l=n.clientWidth,i=n.clientHeight,a=0,s=0;if(o){l=o.width,i=o.height;let e=v();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:l,height:i,x:a,y:s}}(e,r);else if("document"===t)n=function(e){let t=u(e),r=k(e),n=e.ownerDocument.body,o=N(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),l=N(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),i=-r.scrollLeft+J(e),a=-r.scrollTop;return"rtl"===w(n).direction&&(i+=N(t.clientWidth,n.clientWidth)-o),{width:o,height:l,x:i,y:a}}(u(e));else if(p(t))n=function(e,t){let r=U(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,l=m(e)?q(e):T(1),i=e.clientWidth*l.x;return{width:i,height:e.clientHeight*l.y,x:o*l.x,y:n*l.y}}(t,r);else{let r=K(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return F(n)}function et(e){return"static"===w(e).position}function er(e,t){if(!m(e)||"fixed"===w(e).position)return null;if(t)return t(e);let r=e.offsetParent;return u(e)===r&&(r=r.ownerDocument.body),r}function en(e,t){let r=d(e);if(b(e))return r;if(!m(e)){let t=C(e);for(;t&&!y(t);){if(p(t)&&!et(t))return t;t=C(t)}return r}let n=er(e,t);for(;n&&["table","td","th"].includes(c(n))&&et(n);)n=er(n,t);return n&&y(n)&&et(n)&&!x(n)?r:n||function(e){let t=C(e);for(;m(t)&&!y(t);){if(x(t))return t;if(b(t))break;t=C(t)}return null}(e)||r}let eo=async function(e){let t=this.getOffsetParent||en,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=m(t),o=u(t),l="fixed"===r,i=U(e,!0,l,t),a={scrollLeft:0,scrollTop:0},s=T(0);if(n||!n&&!l){if(("body"!==c(t)||h(o))&&(a=k(t)),n){let e=U(t,!0,l,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=J(o))}l&&!n&&o&&(s.x=J(o));let d=!o||n||l?T(0):Q(o,a);return{x:i.left+a.scrollLeft-s.x-d.x,y:i.top+a.scrollTop-s.y-d.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},el={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,l="fixed"===o,i=u(n),a=!!t&&b(t.floating);if(n===i||a&&l)return r;let s={scrollLeft:0,scrollTop:0},d=T(1),f=T(0),p=m(n);if((p||!p&&!l)&&(("body"!==c(n)||h(i))&&(s=k(n)),m(n))){let e=U(n);d=q(n),f.x=e.x+n.clientLeft,f.y=e.y+n.clientTop}let g=!i||p||l?T(0):Q(i,s,!0);return{width:r.width*d.x,height:r.height*d.y,x:r.x*d.x-s.scrollLeft*d.x+f.x+g.x,y:r.y*d.y-s.scrollTop*d.y+f.y+g.y}},getDocumentElement:u,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,l=[..."clippingAncestors"===r?b(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=E(e,[],!1).filter(e=>p(e)&&"body"!==c(e)),o=null,l="fixed"===w(e).position,i=l?C(e):e;for(;p(i)&&!y(i);){let t=w(i),r=x(i);r||"fixed"!==t.position||(o=null),(l?!r&&!o:!r&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||h(i)&&!r&&function e(t,r){let n=C(t);return!(n===r||!p(n)||y(n))&&("fixed"===w(n).position||e(n,r))}(e,i))?n=n.filter(e=>e!==i):o=t,i=C(i)}return t.set(e,n),n}(t,this._c):[].concat(r),n],i=l[0],a=l.reduce((e,r)=>{let n=ee(t,r,o);return e.top=N(n.top,e.top),e.right=R(n.right,e.right),e.bottom=R(n.bottom,e.bottom),e.left=N(n.left,e.left),e},ee(t,i,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:en,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=H(e);return{width:t,height:r}},getScale:q,isElement:p,isRTL:function(e){return"rtl"===w(e).direction}};function ei(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function ea(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:l=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=n,d=W(e),f=l||i?[...d?E(d):[],...E(t)]:[];f.forEach(e=>{l&&e.addEventListener("scroll",r,{passive:!0}),i&&e.addEventListener("resize",r)});let p=d&&s?function(e,t){let r,n=null,o=u(e);function l(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function i(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),l();let c=e.getBoundingClientRect(),{left:d,top:u,width:f,height:p}=c;if(a||t(),!f||!p)return;let m=S(u),g=S(o.clientWidth-(d+f)),h={rootMargin:-m+"px "+-g+"px "+-S(o.clientHeight-(u+p))+"px "+-S(d)+"px",threshold:N(0,R(1,s))||1},b=!0;function x(t){let n=t[0].intersectionRatio;if(n!==s){if(!b)return i();n?i(!1,n):r=setTimeout(()=>{i(!1,1e-7)},1e3)}1!==n||ei(c,e.getBoundingClientRect())||i(),b=!1}try{n=new IntersectionObserver(x,{...h,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(x,h)}n.observe(e)}(!0),l}(d,r):null,m=-1,g=null;a&&(g=new ResizeObserver(e=>{let[n]=e;n&&n.target===d&&g&&(g.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),r()}),d&&!c&&g.observe(d),g.observe(t));let h=c?U(e):null;return c&&function t(){let n=U(e);h&&!ei(h,n)&&r(),h=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;f.forEach(e=>{l&&e.removeEventListener("scroll",r),i&&e.removeEventListener("resize",r)}),null==p||p(),null==(e=g)||e.disconnect(),g=null,c&&cancelAnimationFrame(o)}}let es=(e,t,r)=>{let n=new Map,o={platform:el,...r},l={...o.platform,_c:n};return G(e,t,{...o,platform:l})};var ec="undefined"!=typeof document?o.useLayoutEffect:o.useEffect;function ed(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!=t.length)return!1;for(n=r;0!=n--;)if(!ed(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!Object.prototype.hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!ed(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function eu(e){let t=o.useRef(e);return ec(()=>{t.current=e}),t}var ef="undefined"!=typeof document?o.useLayoutEffect:o.useEffect;let ep=!1,em=0,eg=()=>"floating-ui-"+em++,eh=o["useId".toString()]||function(){let[e,t]=o.useState(()=>ep?eg():void 0);return ef(()=>{null==e&&t(eg())},[]),o.useEffect(()=>{ep||(ep=!0)},[]),e},eb=o.createContext(null),ex=o.createContext(null),ev=()=>{var e;return(null==(e=o.useContext(eb))?void 0:e.id)||null},ey=()=>o.useContext(ex);function ew(e){return(null==e?void 0:e.ownerDocument)||document}function ek(e){return ew(e).defaultView||window}function eC(e){return!!e&&e instanceof ek(e).Element}function eE(e){return!!e&&e instanceof ek(e).HTMLElement}function ej(e,t){let r=["mouse","pen"];return t||r.push("",void 0),r.includes(e)}function eR(e){let t=(0,o.useRef)(e);return ef(()=>{t.current=e}),t}let eN="data-floating-ui-safe-polygon";function eP(e,t,r){return r&&!ej(r)?0:"number"==typeof e?e:null==e?void 0:e[t]}let eS=function(e,t){let{enabled:r=!0,delay:n=0,handleClose:l=null,mouseOnly:i=!1,restMs:a=0,move:s=!0}=void 0===t?{}:t,{open:c,onOpenChange:d,dataRef:u,events:f,elements:{domReference:p,floating:m},refs:g}=e,h=ey(),b=ev(),x=eR(l),v=eR(n),y=o.useRef(),w=o.useRef(),k=o.useRef(),C=o.useRef(),E=o.useRef(!0),j=o.useRef(!1),R=o.useRef(()=>{}),N=o.useCallback(()=>{var e;let t=null==(e=u.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t},[u]);o.useEffect(()=>{if(r)return f.on("dismiss",e),()=>{f.off("dismiss",e)};function e(){clearTimeout(w.current),clearTimeout(C.current),E.current=!0}},[r,f]),o.useEffect(()=>{if(!r||!x.current||!c)return;function e(){N()&&d(!1)}let t=ew(m).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}},[m,c,d,r,x,u,N]);let P=o.useCallback(function(e){void 0===e&&(e=!0);let t=eP(v.current,"close",y.current);t&&!k.current?(clearTimeout(w.current),w.current=setTimeout(()=>d(!1),t)):e&&(clearTimeout(w.current),d(!1))},[v,d]),S=o.useCallback(()=>{R.current(),k.current=void 0},[]),T=o.useCallback(()=>{if(j.current){let e=ew(g.floating.current).body;e.style.pointerEvents="",e.removeAttribute(eN),j.current=!1}},[g]);return o.useEffect(()=>{if(r&&eC(p))return c&&p.addEventListener("mouseleave",l),null==m||m.addEventListener("mouseleave",l),s&&p.addEventListener("mousemove",n,{once:!0}),p.addEventListener("mouseenter",n),p.addEventListener("mouseleave",o),()=>{c&&p.removeEventListener("mouseleave",l),null==m||m.removeEventListener("mouseleave",l),s&&p.removeEventListener("mousemove",n),p.removeEventListener("mouseenter",n),p.removeEventListener("mouseleave",o)};function t(){return!!u.current.openEvent&&["click","mousedown"].includes(u.current.openEvent.type)}function n(e){if(clearTimeout(w.current),E.current=!1,i&&!ej(y.current)||a>0&&0===eP(v.current,"open"))return;u.current.openEvent=e;let t=eP(v.current,"open",y.current);t?w.current=setTimeout(()=>{d(!0)},t):d(!0)}function o(r){if(t())return;R.current();let n=ew(m);if(clearTimeout(C.current),x.current){c||clearTimeout(w.current),k.current=x.current({...e,tree:h,x:r.clientX,y:r.clientY,onClose(){T(),S(),P()}});let t=k.current;n.addEventListener("mousemove",t),R.current=()=>{n.removeEventListener("mousemove",t)};return}P()}function l(r){t()||null==x.current||x.current({...e,tree:h,x:r.clientX,y:r.clientY,onClose(){T(),S(),P()}})(r)}},[p,m,r,e,i,a,s,P,S,T,d,c,h,v,x,u]),ef(()=>{var e,t,n;if(r&&c&&null!=(e=x.current)&&e.__options.blockPointerEvents&&N()){let e=ew(m).body;if(e.setAttribute(eN,""),e.style.pointerEvents="none",j.current=!0,eC(p)&&m){let e=null==h?void 0:null==(t=h.nodesRef.current.find(e=>e.id===b))?void 0:null==(n=t.context)?void 0:n.elements.floating;return e&&(e.style.pointerEvents=""),p.style.pointerEvents="auto",m.style.pointerEvents="auto",()=>{p.style.pointerEvents="",m.style.pointerEvents=""}}}},[r,c,b,m,p,h,x,u,N]),ef(()=>{c||(y.current=void 0,S(),T())},[c,S,T]),o.useEffect(()=>()=>{S(),clearTimeout(w.current),clearTimeout(C.current),T()},[r,S,T]),o.useMemo(()=>{if(!r)return{};function e(e){y.current=e.pointerType}return{reference:{onPointerDown:e,onPointerEnter:e,onMouseMove(){c||0===a||(clearTimeout(C.current),C.current=setTimeout(()=>{E.current||d(!0)},a))}},floating:{onMouseEnter(){clearTimeout(w.current)},onMouseLeave(){f.emit("dismiss",{type:"mouseLeave",data:{returnFocus:!1}}),P(!1)}}}},[f,r,a,c,d,P])};function eT(e,t){if(!e||!t)return!1;let r=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(r&&function(e){if("undefined"==typeof ShadowRoot)return!1;let t=ek(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}(r)){let r=t;do{if(r&&e===r)return!0;r=r.parentNode||r.host}while(r)}return!1}function eL(e,t){let r=e.filter(e=>{var r;return e.parentId===t&&(null==(r=e.context)?void 0:r.open)})||[],n=r;for(;n.length;)n=e.filter(e=>{var t;return null==(t=n)?void 0:t.some(t=>{var r;return e.parentId===t.id&&(null==(r=e.context)?void 0:r.open)})})||[],r=r.concat(n);return r}let e$=o["useInsertionEffect".toString()]||(e=>e());function ez(e){let t=o.useRef(()=>{});return e$(()=>{t.current=e}),o.useCallback(function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return null==t.current?void 0:t.current(...r)},[])}function eM(e,t){return null!=t&&("composedPath"in e?e.composedPath().includes(t):null!=e.target&&t.contains(e.target))}let e_={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},eO={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},eB=function(e){var t,r;return void 0===e&&(e=!0),{escapeKeyBubbles:"boolean"==typeof e?e:null==(t=e.escapeKey)||t,outsidePressBubbles:"boolean"==typeof e?e:null==(r=e.outsidePress)||r}},eA=function(e,t){let{open:r,onOpenChange:n,events:l,nodeId:i,elements:{reference:a,domReference:s,floating:c},dataRef:d}=e,{enabled:u=!0,escapeKey:f=!0,outsidePress:p=!0,outsidePressEvent:m="pointerdown",referencePress:g=!1,referencePressEvent:h="pointerdown",ancestorScroll:b=!1,bubbles:x=!0}=void 0===t?{}:t,v=ey(),y=null!=ev(),w=ez("function"==typeof p?p:()=>!1),k="function"==typeof p?w:p,C=o.useRef(!1),{escapeKeyBubbles:j,outsidePressBubbles:R}=eB(x);return o.useEffect(()=>{if(!r||!u)return;function e(e){if("Escape"===e.key){let e=v?eL(v.nodesRef.current,i):[];if(e.length>0){let t=!0;if(e.forEach(e=>{var r;if(null!=(r=e.context)&&r.open&&!e.context.dataRef.current.__escapeKeyBubbles){t=!1;return}}),!t)return}l.emit("dismiss",{type:"escapeKey",data:{returnFocus:{preventScroll:!1}}}),n(!1)}}function t(e){var t;let r=C.current;if(C.current=!1,r||"function"==typeof k&&!k(e))return;let o="composedPath"in e?e.composedPath()[0]:e.target;if(eE(o)&&c){let t=c.ownerDocument.defaultView||window,r=o.scrollWidth>o.clientWidth,n=o.scrollHeight>o.clientHeight,l=n&&e.offsetX>o.clientWidth;if(n&&"rtl"===t.getComputedStyle(o).direction&&(l=e.offsetX<=o.offsetWidth-o.clientWidth),l||r&&e.offsetY>o.clientHeight)return}let a=v&&eL(v.nodesRef.current,i).some(t=>{var r;return eM(e,null==(r=t.context)?void 0:r.elements.floating)});if(eM(e,c)||eM(e,s)||a)return;let d=v?eL(v.nodesRef.current,i):[];if(d.length>0){let e=!0;if(d.forEach(t=>{var r;if(null!=(r=t.context)&&r.open&&!t.context.dataRef.current.__outsidePressBubbles){e=!1;return}}),!e)return}l.emit("dismiss",{type:"outsidePress",data:{returnFocus:y?{preventScroll:!0}:function(e){if(0===e.mozInputSource&&e.isTrusted)return!0;let t=/Android/i;return(t.test(function(){let e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}())||t.test(function(){let e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(e=>{let{brand:t,version:r}=e;return t+"/"+r}).join(" "):navigator.userAgent}()))&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType}(e)||0===(t=e).width&&0===t.height||1===t.width&&1===t.height&&0===t.pressure&&0===t.detail&&"mouse"!==t.pointerType||t.width<1&&t.height<1&&0===t.pressure&&0===t.detail}}),n(!1)}function o(){n(!1)}d.current.__escapeKeyBubbles=j,d.current.__outsidePressBubbles=R;let p=ew(c);f&&p.addEventListener("keydown",e),k&&p.addEventListener(m,t);let g=[];return b&&(eC(s)&&(g=E(s)),eC(c)&&(g=g.concat(E(c))),!eC(a)&&a&&a.contextElement&&(g=g.concat(E(a.contextElement)))),(g=g.filter(e=>{var t;return e!==(null==(t=p.defaultView)?void 0:t.visualViewport)})).forEach(e=>{e.addEventListener("scroll",o,{passive:!0})}),()=>{f&&p.removeEventListener("keydown",e),k&&p.removeEventListener(m,t),g.forEach(e=>{e.removeEventListener("scroll",o)})}},[d,c,s,a,f,k,m,l,v,i,r,n,b,u,j,R,y]),o.useEffect(()=>{C.current=!1},[k,m]),o.useMemo(()=>u?{reference:{[e_[h]]:()=>{g&&(l.emit("dismiss",{type:"referencePress",data:{returnFocus:!1}}),n(!1))}},floating:{[eO[m]]:()=>{C.current=!0}}}:{},[u,l,g,m,h,n])},eD=function(e,t){let{open:r,onOpenChange:n,dataRef:l,events:i,refs:a,elements:{floating:s,domReference:c}}=e,{enabled:d=!0,keyboardOnly:u=!0}=void 0===t?{}:t,f=o.useRef(""),p=o.useRef(!1),m=o.useRef();return o.useEffect(()=>{if(!d)return;let e=ew(s).defaultView||window;function t(){!r&&eE(c)&&c===function(e){let t=e.activeElement;for(;(null==(r=t)?void 0:null==(n=r.shadowRoot)?void 0:n.activeElement)!=null;){var r,n;t=t.shadowRoot.activeElement}return t}(ew(c))&&(p.current=!0)}return e.addEventListener("blur",t),()=>{e.removeEventListener("blur",t)}},[s,c,r,d]),o.useEffect(()=>{if(d)return i.on("dismiss",e),()=>{i.off("dismiss",e)};function e(e){("referencePress"===e.type||"escapeKey"===e.type)&&(p.current=!0)}},[i,d]),o.useEffect(()=>()=>{clearTimeout(m.current)},[]),o.useMemo(()=>d?{reference:{onPointerDown(e){let{pointerType:t}=e;f.current=t,p.current=!!(t&&u)},onMouseLeave(){p.current=!1},onFocus(e){var t;p.current||"focus"===e.type&&(null==(t=l.current.openEvent)?void 0:t.type)==="mousedown"&&l.current.openEvent&&eM(l.current.openEvent,c)||(l.current.openEvent=e.nativeEvent,n(!0))},onBlur(e){p.current=!1;let t=e.relatedTarget,r=eC(t)&&t.hasAttribute("data-floating-ui-focus-guard")&&"outside"===t.getAttribute("data-type");m.current=setTimeout(()=>{eT(a.floating.current,t)||eT(c,t)||r||n(!1)})}}}:{},[d,u,c,a,l,n])},eI=function(e,t){let{open:r}=e,{enabled:n=!0,role:l="dialog"}=void 0===t?{}:t,i=eh(),a=eh();return o.useMemo(()=>{let e={id:i,role:l};return n?"tooltip"===l?{reference:{"aria-describedby":r?i:void 0},floating:e}:{reference:{"aria-expanded":r?"true":"false","aria-haspopup":"alertdialog"===l?"dialog":l,"aria-controls":r?i:void 0,..."listbox"===l&&{role:"combobox"},..."menu"===l&&{id:a}},floating:{...e,..."menu"===l&&{"aria-labelledby":a}}}:{}},[n,l,r,i,a])};function eF(e,t,r){let n=new Map;return{..."floating"===r&&{tabIndex:-1},...e,...t.map(e=>e?e[r]:null).concat(e).reduce((e,t)=>(t&&Object.entries(t).forEach(t=>{let[r,o]=t;if(0===r.indexOf("on")){if(n.has(r)||n.set(r,[]),"function"==typeof o){var l;null==(l=n.get(r))||l.push(o),e[r]=function(){for(var e,t=arguments.length,o=Array(t),l=0;l<t;l++)o[l]=arguments[l];null==(e=n.get(r))||e.forEach(e=>e(...o))}}}else e[r]=o}),e),{})}}let eV=function(e){void 0===e&&(e=[]);let t=e,r=o.useCallback(t=>eF(t,e,"reference"),t),n=o.useCallback(t=>eF(t,e,"floating"),t),l=o.useCallback(t=>eF(t,e,"item"),e.map(e=>null==e?void 0:e.item));return o.useMemo(()=>({getReferenceProps:r,getFloatingProps:n,getItemProps:l}),[r,n,l])},eG=e=>{let t=eW(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),eX(r,t)||eH(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},eX=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),o=n?eX(e.slice(1),n):void 0;if(o)return o;if(0===t.validators.length)return;let l=e.join("-");return t.validators.find(({validator:e})=>e(l))?.classGroupId},eY=/^\[(.+)\]$/,eH=e=>{if(eY.test(e)){let t=eY.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},eW=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return eU(Object.entries(e.classGroups),r).forEach(([e,r])=>{eq(r,n,e,t)}),n},eq=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:eZ(t,e)).classGroupId=r;return}if("function"==typeof e){if(eK(e)){eq(e(n),t,r,n);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,o])=>{eq(o,eZ(t,e),r,n)})})},eZ=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},eK=e=>e.isThemeGetter,eU=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,eJ=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,l)=>{r.set(o,l),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},eQ=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,o=t[0],l=t.length,i=e=>{let r;let i=[],a=0,s=0;for(let c=0;c<e.length;c++){let d=e[c];if(0===a){if(d===o&&(n||e.slice(c,c+l)===t)){i.push(e.slice(s,c)),s=c+l;continue}if("/"===d){r=c;continue}}"["===d?a++:"]"===d&&a--}let c=0===i.length?e:e.substring(s),d=c.startsWith("!"),u=d?c.substring(1):c;return{modifiers:i,hasImportantModifier:d,baseClassName:u,maybePostfixModifierPosition:r&&r>s?r-s:void 0}};return r?e=>r({className:e,parseClassName:i}):i},e0=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},e1=e=>({cache:eJ(e.cacheSize),parseClassName:eQ(e),...eG(e)}),e2=/\s+/,e7=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o}=t,l=[],i=e.trim().split(e2),a="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{modifiers:s,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:u}=r(t),f=!!u,p=n(f?d.substring(0,u):d);if(!p){if(!f||!(p=n(d))){a=t+(a.length>0?" "+a:a);continue}f=!1}let m=e0(s).join(":"),g=c?m+"!":m,h=g+p;if(l.includes(h))continue;l.push(h);let b=o(p,f);for(let e=0;e<b.length;++e){let t=b[e];l.push(g+t)}a=t+(a.length>0?" "+a:a)}return a};function e5(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=e6(e))&&(n&&(n+=" "),n+=t);return n}let e6=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=e6(e[n]))&&(r&&(r+=" "),r+=t);return r};function e3(e,...t){let r,n,o;let l=function(a){return n=(r=e1(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,l=i,i(a)};function i(e){let t=n(e);if(t)return t;let l=e7(e,r);return o(e,l),l}return function(){return l(e5.apply(null,arguments))}}let e4=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},e9=/^\[(?:([a-z-]+):)?(.+)\]$/i,e8=/^\d+\/\d+$/,te=new Set(["px","full","screen"]),tt=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,tr=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,tn=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,to=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,tl=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,ti=e=>ts(e)||te.has(e)||e8.test(e),ta=e=>tw(e,"length",tk),ts=e=>!!e&&!Number.isNaN(Number(e)),tc=e=>tw(e,"number",ts),td=e=>!!e&&Number.isInteger(Number(e)),tu=e=>e.endsWith("%")&&ts(e.slice(0,-1)),tf=e=>e9.test(e),tp=e=>tt.test(e),tm=new Set(["length","size","percentage"]),tg=e=>tw(e,tm,tC),th=e=>tw(e,"position",tC),tb=new Set(["image","url"]),tx=e=>tw(e,tb,tj),tv=e=>tw(e,"",tE),ty=()=>!0,tw=(e,t,r)=>{let n=e9.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},tk=e=>tr.test(e)&&!tn.test(e),tC=()=>!1,tE=e=>to.test(e),tj=e=>tl.test(e);Symbol.toStringTag;let tR=()=>{let e=e4("colors"),t=e4("spacing"),r=e4("blur"),n=e4("brightness"),o=e4("borderColor"),l=e4("borderRadius"),i=e4("borderSpacing"),a=e4("borderWidth"),s=e4("contrast"),c=e4("grayscale"),d=e4("hueRotate"),u=e4("invert"),f=e4("gap"),p=e4("gradientColorStops"),m=e4("gradientColorStopPositions"),g=e4("inset"),h=e4("margin"),b=e4("opacity"),x=e4("padding"),v=e4("saturate"),y=e4("scale"),w=e4("sepia"),k=e4("skew"),C=e4("space"),E=e4("translate"),j=()=>["auto","contain","none"],R=()=>["auto","hidden","clip","visible","scroll"],N=()=>["auto",tf,t],P=()=>[tf,t],S=()=>["",ti,ta],T=()=>["auto",ts,tf],L=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],$=()=>["solid","dashed","dotted","double","none"],z=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],M=()=>["start","end","center","between","around","evenly","stretch"],_=()=>["","0",tf],O=()=>["auto","avoid","all","avoid-page","page","left","right","column"],B=()=>[ts,tf];return{cacheSize:500,separator:":",theme:{colors:[ty],spacing:[ti,ta],blur:["none","",tp,tf],brightness:B(),borderColor:[e],borderRadius:["none","","full",tp,tf],borderSpacing:P(),borderWidth:S(),contrast:B(),grayscale:_(),hueRotate:B(),invert:_(),gap:P(),gradientColorStops:[e],gradientColorStopPositions:[tu,ta],inset:N(),margin:N(),opacity:B(),padding:P(),saturate:B(),scale:B(),sepia:_(),skew:B(),space:P(),translate:P()},classGroups:{aspect:[{aspect:["auto","square","video",tf]}],container:["container"],columns:[{columns:[tp]}],"break-after":[{"break-after":O()}],"break-before":[{"break-before":O()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...L(),tf]}],overflow:[{overflow:R()}],"overflow-x":[{"overflow-x":R()}],"overflow-y":[{"overflow-y":R()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[g]}],"inset-x":[{"inset-x":[g]}],"inset-y":[{"inset-y":[g]}],start:[{start:[g]}],end:[{end:[g]}],top:[{top:[g]}],right:[{right:[g]}],bottom:[{bottom:[g]}],left:[{left:[g]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",td,tf]}],basis:[{basis:N()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",tf]}],grow:[{grow:_()}],shrink:[{shrink:_()}],order:[{order:["first","last","none",td,tf]}],"grid-cols":[{"grid-cols":[ty]}],"col-start-end":[{col:["auto",{span:["full",td,tf]},tf]}],"col-start":[{"col-start":T()}],"col-end":[{"col-end":T()}],"grid-rows":[{"grid-rows":[ty]}],"row-start-end":[{row:["auto",{span:[td,tf]},tf]}],"row-start":[{"row-start":T()}],"row-end":[{"row-end":T()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",tf]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",tf]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...M()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...M(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...M(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[x]}],px:[{px:[x]}],py:[{py:[x]}],ps:[{ps:[x]}],pe:[{pe:[x]}],pt:[{pt:[x]}],pr:[{pr:[x]}],pb:[{pb:[x]}],pl:[{pl:[x]}],m:[{m:[h]}],mx:[{mx:[h]}],my:[{my:[h]}],ms:[{ms:[h]}],me:[{me:[h]}],mt:[{mt:[h]}],mr:[{mr:[h]}],mb:[{mb:[h]}],ml:[{ml:[h]}],"space-x":[{"space-x":[C]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[C]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",tf,t]}],"min-w":[{"min-w":[tf,t,"min","max","fit"]}],"max-w":[{"max-w":[tf,t,"none","full","min","max","fit","prose",{screen:[tp]},tp]}],h:[{h:[tf,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[tf,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[tf,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[tf,t,"auto","min","max","fit"]}],"font-size":[{text:["base",tp,ta]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",tc]}],"font-family":[{font:[ty]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",tf]}],"line-clamp":[{"line-clamp":["none",ts,tc]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",ti,tf]}],"list-image":[{"list-image":["none",tf]}],"list-style-type":[{list:["none","disc","decimal",tf]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[b]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[b]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...$(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",ti,ta]}],"underline-offset":[{"underline-offset":["auto",ti,tf]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",tf]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",tf]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[b]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...L(),th]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",tg]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},tx]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[b]}],"border-style":[{border:[...$(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[b]}],"divide-style":[{divide:$()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...$()]}],"outline-offset":[{"outline-offset":[ti,tf]}],"outline-w":[{outline:[ti,ta]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:S()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[b]}],"ring-offset-w":[{"ring-offset":[ti,ta]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",tp,tv]}],"shadow-color":[{shadow:[ty]}],opacity:[{opacity:[b]}],"mix-blend":[{"mix-blend":[...z(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":z()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",tp,tf]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[u]}],saturate:[{saturate:[v]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[b]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",tf]}],duration:[{duration:B()}],ease:[{ease:["linear","in","out","in-out",tf]}],delay:[{delay:B()}],animate:[{animate:["none","spin","ping","pulse","bounce",tf]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[y]}],"scale-x":[{"scale-x":[y]}],"scale-y":[{"scale-y":[y]}],rotate:[{rotate:[td,tf]}],"translate-x":[{"translate-x":[E]}],"translate-y":[{"translate-y":[E]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",tf]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",tf]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",tf]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[ti,ta,tc]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},tN=(e,{cacheSize:t,prefix:r,separator:n,experimentalParseClassName:o,extend:l={},override:i={}})=>{for(let l in tP(e,"cacheSize",t),tP(e,"prefix",r),tP(e,"separator",n),tP(e,"experimentalParseClassName",o),i)tS(e[l],i[l]);for(let t in l)tT(e[t],l[t]);return e},tP=(e,t,r)=>{void 0!==r&&(e[t]=r)},tS=(e,t)=>{if(t)for(let r in t)tP(e,r,t[r])},tT=(e,t)=>{if(t)for(let r in t){let n=t[r];void 0!==n&&(e[r]=(e[r]||[]).concat(n))}},tL=((e,...t)=>"function"==typeof e?e3(tR,e,...t):e3(()=>tN(tR(),e),...t))({extend:{classGroups:{shadow:[{shadow:[{tremor:["input","card","dropdown"],"dark-tremor":["input","card","dropdown"]}]}],rounded:[{rounded:[{tremor:["small","default","full"],"dark-tremor":["small","default","full"]}]}],"font-size":[{text:[{tremor:["default","title","metric"],"dark-tremor":["default","title","metric"]}]}]}}}),t$=e=>{let[t,r]=(0,o.useState)(!1),[n,l]=(0,o.useState)(),{x:i,y:s,refs:c,strategy:d,context:u}=function(e){void 0===e&&(e={});let{open:t=!1,onOpenChange:r,nodeId:n}=e,l=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:l,whileElementsMounted:i,open:s}=e,[c,d]=o.useState({x:null,y:null,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[u,f]=o.useState(n);ed(u,n)||f(n);let p=o.useRef(null),m=o.useRef(null),g=o.useRef(c),h=eu(i),b=eu(l),[x,v]=o.useState(null),[y,w]=o.useState(null),k=o.useCallback(e=>{p.current!==e&&(p.current=e,v(e))},[]),C=o.useCallback(e=>{m.current!==e&&(m.current=e,w(e))},[]),E=o.useCallback(()=>{if(!p.current||!m.current)return;let e={placement:t,strategy:r,middleware:u};b.current&&(e.platform=b.current),es(p.current,m.current,e).then(e=>{let t={...e,isPositioned:!0};j.current&&!ed(g.current,t)&&(g.current=t,a.flushSync(()=>{d(t)}))})},[u,t,r,b]);ec(()=>{!1===s&&g.current.isPositioned&&(g.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let j=o.useRef(!1);ec(()=>(j.current=!0,()=>{j.current=!1}),[]),ec(()=>{if(x&&y){if(h.current)return h.current(x,y,E);E()}},[x,y,E,h]);let R=o.useMemo(()=>({reference:p,floating:m,setReference:k,setFloating:C}),[k,C]),N=o.useMemo(()=>({reference:x,floating:y}),[x,y]);return o.useMemo(()=>({...c,update:E,refs:R,elements:N,reference:k,floating:C}),[c,E,R,N,k,C])}(e),i=ey(),s=o.useRef(null),c=o.useRef({}),d=o.useState(()=>(function(){let e=new Map;return{emit(t,r){var n;null==(n=e.get(t))||n.forEach(e=>e(r))},on(t,r){e.set(t,[...e.get(t)||[],r])},off(t,r){e.set(t,(e.get(t)||[]).filter(e=>e!==r))}}})())[0],[u,f]=o.useState(null),p=o.useCallback(e=>{let t=eC(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;l.refs.setReference(t)},[l.refs]),m=o.useCallback(e=>{(eC(e)||null===e)&&(s.current=e,f(e)),(eC(l.refs.reference.current)||null===l.refs.reference.current||null!==e&&!eC(e))&&l.refs.setReference(e)},[l.refs]),g=o.useMemo(()=>({...l.refs,setReference:m,setPositionReference:p,domReference:s}),[l.refs,m,p]),h=o.useMemo(()=>({...l.elements,domReference:u}),[l.elements,u]),b=ez(r),x=o.useMemo(()=>({...l,refs:g,elements:h,dataRef:c,nodeId:n,events:d,open:t,onOpenChange:b}),[l,n,d,t,b,g,h]);return ef(()=>{let e=null==i?void 0:i.nodesRef.current.find(e=>e.id===n);e&&(e.context=x)}),o.useMemo(()=>({...l,context:x,refs:g,reference:m,positionReference:p}),[l,g,x,m,p])}({open:t,onOpenChange:t=>{t&&e?l(setTimeout(()=>{r(t)},e)):(clearTimeout(n),r(t))},placement:"top",whileElementsMounted:ea,middleware:[function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:l,placement:i,middlewareData:a}=t,s=await Y(t,e);return i===(null==(r=a.offset)?void 0:r.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:o+s.x,y:l+s.y,data:{...s,placement:i}}}}}(5),function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,l,i,a;let{placement:s,middlewareData:c,rects:d,initialPlacement:u,platform:f,elements:p}=t,{mainAxis:m=!0,crossAxis:g=!0,fallbackPlacements:h,fallbackStrategy:b="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:v=!0,...y}=z(e,t);if(null!=(r=c.arrow)&&r.alignmentOffset)return{};let w=M(s),k=A(u),C=M(u)===u,E=await (null==f.isRTL?void 0:f.isRTL(p.floating)),j=h||(C||!v?[I(u)]:function(e){let t=I(e);return[D(e),t,D(t)]}(u)),R="none"!==x;!h&&R&&j.push(...function(e,t,r,n){let o=_(e),l=function(e,t,r){let n=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(r)return t?o:n;return t?n:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(M(e),"start"===r,n);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(D)))),l}(u,v,x,E));let N=[u,...j],P=await X(t,y),S=[],T=(null==(n=c.flip)?void 0:n.overflows)||[];if(m&&S.push(P[w]),g){let e=function(e,t,r){void 0===r&&(r=!1);let n=_(e),o=O(A(e)),l=B(o),i="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=I(i)),[i,I(i)]}(s,d,E);S.push(P[e[0]],P[e[1]])}if(T=[...T,{placement:s,overflows:S}],!S.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=N[e];if(t){let r="alignment"===g&&k!==A(t),n=(null==(i=T[0])?void 0:i.overflows[0])>0;if(!r||n)return{data:{index:e,overflows:T},reset:{placement:t}}}let r=null==(l=T.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!r)switch(b){case"bestFit":{let e=null==(a=T.filter(e=>{if(R){let t=A(e.placement);return t===k||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(r=e);break}case"initialPlacement":r=u}if(s!==r)return{reset:{placement:r}}}return{}}}}({fallbackAxisSideDirection:"start"}),function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:l=!0,crossAxis:i=!1,limiter:a={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...s}=z(e,t),c={x:r,y:n},d=await X(t,s),u=A(M(o)),f=O(u),p=c[f],m=c[u];if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=p+d[e],n=p-d[t];p=N(r,R(p,n))}if(i){let e="y"===u?"top":"left",t="y"===u?"bottom":"right",r=m+d[e],n=m-d[t];m=N(r,R(m,n))}let g=a.fn({...t,[f]:p,[u]:m});return{...g,data:{x:g.x-r,y:g.y-n,enabled:{[f]:l,[u]:i}}}}}}()]}),f=eS(u,{move:!1}),{getReferenceProps:p,getFloatingProps:m}=eV([f,eD(u),eA(u),eI(u,{role:"tooltip"})]);return{tooltipProps:{open:t,x:i,y:s,refs:c,strategy:d,getFloatingProps:m},getReferenceProps:p}},tz=({text:e,open:t,x:r,y:n,refs:o,strategy:i,getFloatingProps:a})=>t&&e?l().createElement("div",Object.assign({className:tL("max-w-xs text-sm z-20 rounded-tremor-default opacity-100 px-2.5 py-1","text-white bg-tremor-background-emphasis","dark:text-tremor-content-emphasis dark:bg-white"),ref:o.setFloating,style:{position:i,top:null!=n?n:0,left:null!=r?r:0}},a()),e):null;tz.displayName="Tooltip";let tM={Slate:"slate",Gray:"gray",Zinc:"zinc",Neutral:"neutral",Stone:"stone",Red:"red",Orange:"orange",Amber:"amber",Yellow:"yellow",Lime:"lime",Green:"green",Emerald:"emerald",Teal:"teal",Cyan:"cyan",Sky:"sky",Blue:"blue",Indigo:"indigo",Violet:"violet",Purple:"purple",Fuchsia:"fuchsia",Pink:"pink",Rose:"rose"},t_={SM:"sm"},tO={Left:"left",Right:"right"},tB={Top:"top",Bottom:"bottom"},tA={background:500,darkBackground:600,border:500,darkBorder:700,iconRing:500,text:500,iconText:600,darkText:700};tM.Blue,tM.Cyan,tM.Sky,tM.Indigo,tM.Violet,tM.Purple,tM.Fuchsia,tM.Slate,tM.Gray,tM.Zinc,tM.Neutral,tM.Stone,tM.Red,tM.Orange,tM.Amber,tM.Yellow,tM.Lime,tM.Green,tM.Emerald,tM.Teal,tM.Pink,tM.Rose;let tD=["slate","gray","zinc","neutral","stone","red","orange","amber","yellow","lime","green","emerald","teal","cyan","sky","blue","indigo","violet","purple","fuchsia","pink","rose"],tI=e=>tD.includes(e);function tF(e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}}function tV(e){return t=>`tremor-${e}-${t}`}function tG(e,t){let r=tI(e);if("white"===e||"black"===e||"transparent"===e||!t||!r){let t=e.includes("#")||e.includes("--")||e.includes("rgb")?`[${e}]`:e;return{bgColor:`bg-${t} dark:bg-${t}`,hoverBgColor:`hover:bg-${t} dark:hover:bg-${t}`,selectBgColor:`data-[selected]:bg-${t} dark:data-[selected]:bg-${t}`,textColor:`text-${t} dark:text-${t}`,selectTextColor:`data-[selected]:text-${t} dark:data-[selected]:text-${t}`,hoverTextColor:`hover:text-${t} dark:hover:text-${t}`,borderColor:`border-${t} dark:border-${t}`,selectBorderColor:`data-[selected]:border-${t} dark:data-[selected]:border-${t}`,hoverBorderColor:`hover:border-${t} dark:hover:border-${t}`,ringColor:`ring-${t} dark:ring-${t}`,strokeColor:`stroke-${t} dark:stroke-${t}`,fillColor:`fill-${t} dark:fill-${t}`}}return{bgColor:`bg-${e}-${t} dark:bg-${e}-${t}`,selectBgColor:`data-[selected]:bg-${e}-${t} dark:data-[selected]:bg-${e}-${t}`,hoverBgColor:`hover:bg-${e}-${t} dark:hover:bg-${e}-${t}`,textColor:`text-${e}-${t} dark:text-${e}-${t}`,selectTextColor:`data-[selected]:text-${e}-${t} dark:data-[selected]:text-${e}-${t}`,hoverTextColor:`hover:text-${e}-${t} dark:hover:text-${e}-${t}`,borderColor:`border-${e}-${t} dark:border-${e}-${t}`,selectBorderColor:`data-[selected]:border-${e}-${t} dark:data-[selected]:border-${e}-${t}`,hoverBorderColor:`hover:border-${e}-${t} dark:hover:border-${e}-${t}`,ringColor:`ring-${e}-${t} dark:ring-${e}-${t}`,strokeColor:`stroke-${e}-${t} dark:stroke-${e}-${t}`,fillColor:`fill-${e}-${t} dark:fill-${e}-${t}`}}let tX={xs:{paddingX:"px-2",paddingY:"py-0.5",fontSize:"text-xs"},sm:{paddingX:"px-2.5",paddingY:"py-0.5",fontSize:"text-sm"},md:{paddingX:"px-3",paddingY:"py-0.5",fontSize:"text-md"},lg:{paddingX:"px-3.5",paddingY:"py-0.5",fontSize:"text-lg"},xl:{paddingX:"px-4",paddingY:"py-1",fontSize:"text-xl"}},tY={xs:{height:"h-4",width:"w-4"},sm:{height:"h-4",width:"w-4"},md:{height:"h-4",width:"w-4"},lg:{height:"h-5",width:"w-5"},xl:{height:"h-6",width:"w-6"}},tH=tV("Badge"),tW=l().forwardRef((e,t)=>{let{color:r,icon:n,size:o=t_.SM,tooltip:a,className:s,children:c}=e,d=i(e,["color","icon","size","tooltip","className","children"]),u=n||null,{tooltipProps:f,getReferenceProps:p}=t$();return l().createElement("span",Object.assign({ref:tF([t,f.refs.setReference]),className:tL(tH("root"),"w-max shrink-0 inline-flex justify-center items-center cursor-default rounded-tremor-small ring-1 ring-inset",r?tL(tG(r,tA.background).bgColor,tG(r,tA.iconText).textColor,tG(r,tA.iconRing).ringColor,"bg-opacity-10 ring-opacity-20","dark:bg-opacity-5 dark:ring-opacity-60"):tL("bg-tremor-brand-faint text-tremor-brand-emphasis ring-tremor-brand/20","dark:bg-dark-tremor-brand-muted/50 dark:text-dark-tremor-brand dark:ring-dark-tremor-subtle/20"),tX[o].paddingX,tX[o].paddingY,tX[o].fontSize,s)},p,d),l().createElement(tz,Object.assign({text:a},f)),u?l().createElement(u,{className:tL(tH("icon"),"shrink-0 -ml-1 mr-1.5",tY[o].height,tY[o].width)}):null,l().createElement("span",{className:tL(tH("text"),"whitespace-nowrap")},c))});tW.displayName="Badge";let tq=["preEnter","entering","entered","preExit","exiting","exited","unmounted"],tZ=e=>({_s:e,status:tq[e],isEnter:e<3,isMounted:6!==e,isResolved:2===e||e>4}),tK=e=>e?6:5,tU=(e,t)=>{switch(e){case 1:case 0:return 2;case 4:case 3:return tK(t)}},tJ=e=>"object"==typeof e?[e.enter,e.exit]:[e,e],tQ=(e,t)=>setTimeout(()=>{isNaN(document.body.offsetTop)||e(t+1)},0),t0=(e,t,r,n,o)=>{clearTimeout(n.current);let l=tZ(e);t(l),r.current=l,o&&o({current:l})},t1=({enter:e=!0,exit:t=!0,preEnter:r,preExit:n,timeout:l,initialEntered:i,mountOnEnter:a,unmountOnExit:s,onStateChange:c}={})=>{let[d,u]=(0,o.useState)(()=>tZ(i?2:tK(a))),f=(0,o.useRef)(d),p=(0,o.useRef)(),[m,g]=tJ(l),h=(0,o.useCallback)(()=>{let e=tU(f.current._s,s);e&&t0(e,u,f,p,c)},[c,s]);return[d,(0,o.useCallback)(o=>{let l=e=>{switch(t0(e,u,f,p,c),e){case 1:m>=0&&(p.current=setTimeout(h,m));break;case 4:g>=0&&(p.current=setTimeout(h,g));break;case 0:case 3:p.current=tQ(l,e)}},i=f.current.isEnter;"boolean"!=typeof o&&(o=!i),o?i||l(e?r?0:1:2):i&&l(t?n?3:4:tK(s))},[h,c,e,t,r,n,m,g,s]),h]},t2=e=>{var t=i(e,[]);return l().createElement("svg",Object.assign({},t,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor"}),l().createElement("path",{fill:"none",d:"M0 0h24v24H0z"}),l().createElement("path",{d:"M18.364 5.636L16.95 7.05A7 7 0 1 0 19 12h2a9 9 0 1 1-2.636-6.364z"}))},t7={xs:{height:"h-4",width:"w-4"},sm:{height:"h-5",width:"w-5"},md:{height:"h-5",width:"w-5"},lg:{height:"h-6",width:"w-6"},xl:{height:"h-6",width:"w-6"}},t5=e=>"light"!==e?{xs:{paddingX:"px-2.5",paddingY:"py-1.5",fontSize:"text-xs"},sm:{paddingX:"px-4",paddingY:"py-2",fontSize:"text-sm"},md:{paddingX:"px-4",paddingY:"py-2",fontSize:"text-md"},lg:{paddingX:"px-4",paddingY:"py-2.5",fontSize:"text-lg"},xl:{paddingX:"px-4",paddingY:"py-3",fontSize:"text-xl"}}:{xs:{paddingX:"",paddingY:"",fontSize:"text-xs"},sm:{paddingX:"",paddingY:"",fontSize:"text-sm"},md:{paddingX:"",paddingY:"",fontSize:"text-md"},lg:{paddingX:"",paddingY:"",fontSize:"text-lg"},xl:{paddingX:"",paddingY:"",fontSize:"text-xl"}},t6=(e,t)=>{switch(e){case"primary":return{textColor:t?tG("white").textColor:"text-tremor-brand-inverted dark:text-dark-tremor-brand-inverted",hoverTextColor:t?tG("white").textColor:"text-tremor-brand-inverted dark:text-dark-tremor-brand-inverted",bgColor:t?tG(t,tA.background).bgColor:"bg-tremor-brand dark:bg-dark-tremor-brand",hoverBgColor:t?tG(t,tA.darkBackground).hoverBgColor:"hover:bg-tremor-brand-emphasis dark:hover:bg-dark-tremor-brand-emphasis",borderColor:t?tG(t,tA.border).borderColor:"border-tremor-brand dark:border-dark-tremor-brand",hoverBorderColor:t?tG(t,tA.darkBorder).hoverBorderColor:"hover:border-tremor-brand-emphasis dark:hover:border-dark-tremor-brand-emphasis"};case"secondary":return{textColor:t?tG(t,tA.text).textColor:"text-tremor-brand dark:text-dark-tremor-brand",hoverTextColor:t?tG(t,tA.text).textColor:"hover:text-tremor-brand-emphasis dark:hover:text-dark-tremor-brand-emphasis",bgColor:tG("transparent").bgColor,hoverBgColor:t?tL(tG(t,tA.background).hoverBgColor,"hover:bg-opacity-20 dark:hover:bg-opacity-20"):"hover:bg-tremor-brand-faint dark:hover:bg-dark-tremor-brand-faint",borderColor:t?tG(t,tA.border).borderColor:"border-tremor-brand dark:border-dark-tremor-brand"};case"light":return{textColor:t?tG(t,tA.text).textColor:"text-tremor-brand dark:text-dark-tremor-brand",hoverTextColor:t?tG(t,tA.darkText).hoverTextColor:"hover:text-tremor-brand-emphasis dark:hover:text-dark-tremor-brand-emphasis",bgColor:tG("transparent").bgColor,borderColor:"",hoverBorderColor:""}}},t3=tV("Button"),t4=({loading:e,iconSize:t,iconPosition:r,Icon:n,needMargin:o,transitionStatus:i})=>{let a=o?r===tO.Left?tL("-ml-1","mr-1.5"):tL("-mr-1","ml-1.5"):"",s=tL("w-0 h-0"),c={default:s,entering:s,entered:t,exiting:t,exited:s};return e?l().createElement(t2,{className:tL(t3("icon"),"animate-spin shrink-0",a,c.default,c[i]),style:{transition:"width 150ms"}}):l().createElement(n,{className:tL(t3("icon"),"shrink-0",t,a)})},t9=l().forwardRef((e,t)=>{let{icon:r,iconPosition:n=tO.Left,size:a=t_.SM,color:s,variant:c="primary",disabled:d,loading:u=!1,loadingText:f,children:p,tooltip:m,className:g}=e,h=i(e,["icon","iconPosition","size","color","variant","disabled","loading","loadingText","children","tooltip","className"]),b=u||d,x=void 0!==r||u,v=u&&f,y=!(!p&&!v),w=tL(t7[a].height,t7[a].width),k="light"!==c?tL("rounded-tremor-default border","shadow-tremor-input","dark:shadow-dark-tremor-input"):"",C=t6(c,s),E=t5(c)[a],{tooltipProps:j,getReferenceProps:R}=t$(300),[N,P]=t1({timeout:50});return(0,o.useEffect)(()=>{P(u)},[u]),l().createElement("button",Object.assign({ref:tF([t,j.refs.setReference]),className:tL(t3("root"),"shrink-0 inline-flex justify-center items-center group font-medium outline-none",k,E.paddingX,E.paddingY,E.fontSize,C.textColor,C.bgColor,C.borderColor,C.hoverBorderColor,b?"opacity-50 cursor-not-allowed":tL(t6(c,s).hoverTextColor,t6(c,s).hoverBgColor,t6(c,s).hoverBorderColor),g),disabled:b},R,h),l().createElement(tz,Object.assign({text:m},j)),x&&n!==tO.Right?l().createElement(t4,{loading:u,iconSize:w,iconPosition:n,Icon:r,transitionStatus:N.status,needMargin:y}):null,v||p?l().createElement("span",{className:tL(t3("text"),"text-tremor-default whitespace-nowrap")},v?f:p):null,x&&n===tO.Right?l().createElement(t4,{loading:u,iconSize:w,iconPosition:n,Icon:r,transitionStatus:N.status,needMargin:y}):null)});t9.displayName="Button";let t8=tV("Card"),re=e=>{if(!e)return"";switch(e){case tO.Left:return"border-l-4";case tB.Top:return"border-t-4";case tO.Right:return"border-r-4";case tB.Bottom:return"border-b-4";default:return""}},rt=l().forwardRef((e,t)=>{let{decoration:r="",decorationColor:n,children:o,className:a}=e,s=i(e,["decoration","decorationColor","children","className"]);return l().createElement("div",Object.assign({ref:t,className:tL(t8("root"),"relative w-full text-left ring-1 rounded-tremor-default p-6","bg-tremor-background ring-tremor-ring shadow-tremor-card","dark:bg-dark-tremor-background dark:ring-dark-tremor-ring dark:shadow-dark-tremor-card",n?tG(n,tA.border).borderColor:"border-tremor-brand dark:border-dark-tremor-brand",re(r),a)},s),o)});rt.displayName="Card";let rr=l().forwardRef((e,t)=>{let{color:r,children:n,className:o}=e,a=i(e,["color","children","className"]);return l().createElement("p",Object.assign({ref:t,className:tL("font-semibold text-tremor-metric",r?tG(r,tA.darkText).textColor:"text-tremor-content-strong dark:text-dark-tremor-content-strong",o)},a),n)});rr.displayName="Metric";let rn=l().forwardRef((e,t)=>{let{color:r,className:n,children:o}=e;return l().createElement("p",{ref:t,className:tL("text-tremor-default",r?tG(r,tA.text).textColor:tL("text-tremor-content","dark:text-dark-tremor-content"),n)},o)});rn.displayName="Text";let ro=e=>{var t=i(e,[]);return l().createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor"},t),l().createElement("path",{d:"M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM11 15V17H13V15H11ZM11 7V13H13V7H11Z"}))},rl=e=>{var t=i(e,[]);return l().createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor"},t),l().createElement("path",{d:"M1.18164 12C2.12215 6.87976 6.60812 3 12.0003 3C17.3924 3 21.8784 6.87976 22.8189 12C21.8784 17.1202 17.3924 21 12.0003 21C6.60812 21 2.12215 17.1202 1.18164 12ZM12.0003 17C14.7617 17 17.0003 14.7614 17.0003 12C17.0003 9.23858 14.7617 7 12.0003 7C9.23884 7 7.00026 9.23858 7.00026 12C7.00026 14.7614 9.23884 17 12.0003 17ZM12.0003 15C10.3434 15 9.00026 13.6569 9.00026 12C9.00026 10.3431 10.3434 9 12.0003 9C13.6571 9 15.0003 10.3431 15.0003 12C15.0003 13.6569 13.6571 15 12.0003 15Z"}))},ri=e=>{var t=i(e,[]);return l().createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor"},t),l().createElement("path",{d:"M4.52047 5.93457L1.39366 2.80777L2.80788 1.39355L22.6069 21.1925L21.1927 22.6068L17.8827 19.2968C16.1814 20.3755 14.1638 21.0002 12.0003 21.0002C6.60812 21.0002 2.12215 17.1204 1.18164 12.0002C1.61832 9.62282 2.81932 7.5129 4.52047 5.93457ZM14.7577 16.1718L13.2937 14.7078C12.902 14.8952 12.4634 15.0002 12.0003 15.0002C10.3434 15.0002 9.00026 13.657 9.00026 12.0002C9.00026 11.537 9.10522 11.0984 9.29263 10.7067L7.82866 9.24277C7.30514 10.0332 7.00026 10.9811 7.00026 12.0002C7.00026 14.7616 9.23884 17.0002 12.0003 17.0002C13.0193 17.0002 13.9672 16.6953 14.7577 16.1718ZM7.97446 3.76015C9.22127 3.26959 10.5793 3.00016 12.0003 3.00016C17.3924 3.00016 21.8784 6.87992 22.8189 12.0002C22.5067 13.6998 21.8038 15.2628 20.8068 16.5925L16.947 12.7327C16.9821 12.4936 17.0003 12.249 17.0003 12.0002C17.0003 9.23873 14.7617 7.00016 12.0003 7.00016C11.7514 7.00016 11.5068 7.01833 11.2677 7.05343L7.97446 3.76015Z"}))},ra=e=>["string","number"].includes(typeof e)?e:e instanceof Array?e.map(ra).join(""):"object"==typeof e&&e?ra(e.props.children):void 0,rs=(e,t,r=!1)=>tL(t?"bg-tremor-background-subtle dark:bg-dark-tremor-background-subtle":"bg-tremor-background dark:bg-dark-tremor-background",!t&&"hover:bg-tremor-background-muted dark:hover:bg-dark-tremor-background-muted",e?"text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis":"text-tremor-content dark:text-dark-tremor-content",t&&"text-tremor-content-subtle dark:text-dark-tremor-content-subtle",r&&"text-red-500 placeholder:text-red-500 dark:text-red-500 dark:placeholder:text-red-500",r?"border-red-500 dark:border-red-500":"border-tremor-border dark:border-dark-tremor-border"),rc=l().forwardRef((e,t)=>{let{value:r,defaultValue:n,type:a,placeholder:s="Type...",icon:c,error:d=!1,errorMessage:u,disabled:f=!1,stepper:p,makeInputClassName:m,className:g,onChange:h,onValueChange:b,autoFocus:x,pattern:v}=e,y=i(e,["value","defaultValue","type","placeholder","icon","error","errorMessage","disabled","stepper","makeInputClassName","className","onChange","onValueChange","autoFocus","pattern"]),[w,k]=(0,o.useState)(x||!1),[C,E]=(0,o.useState)(!1),j=(0,o.useCallback)(()=>E(!C),[C,E]),R=(0,o.useRef)(null),N=function(e){return null!=e&&""!==e}(r||n);return l().useEffect(()=>{let e=()=>k(!0),t=()=>k(!1),r=R.current;return r&&(r.addEventListener("focus",e),r.addEventListener("blur",t),x&&r.focus()),()=>{r&&(r.removeEventListener("focus",e),r.removeEventListener("blur",t))}},[x]),l().createElement(l().Fragment,null,l().createElement("div",{className:tL(m("root"),"relative w-full flex items-center min-w-[10rem] outline-none rounded-tremor-default transition duration-100 border","shadow-tremor-input","dark:shadow-dark-tremor-input",rs(N,f,d),w&&tL("ring-2","border-tremor-brand-subtle ring-tremor-brand-muted","dark:border-dark-tremor-brand-subtle dark:ring-dark-tremor-brand-muted"),g)},c?l().createElement(c,{className:tL(m("icon"),"shrink-0 h-5 w-5 mx-2.5 absolute left-0 flex items-center","text-tremor-content-subtle","dark:text-dark-tremor-content-subtle")}):null,l().createElement("input",Object.assign({ref:tF([R,t]),defaultValue:n,value:r,type:C?"text":a,className:tL(m("input"),"w-full bg-transparent focus:outline-none focus:ring-0 border-none text-tremor-default rounded-tremor-default transition duration-100 py-2","text-tremor-content-emphasis","dark:text-dark-tremor-content-emphasis","[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none","password"===a?d?"pr-16":"pr-12":d?"pr-8":"pr-3",c?"pl-10":"pl-3",f?"placeholder:text-tremor-content-subtle dark:placeholder:text-dark-tremor-content-subtle":"placeholder:text-tremor-content dark:placeholder:text-dark-tremor-content"),placeholder:s,disabled:f,"data-testid":"base-input",onChange:e=>{null==h||h(e),null==b||b(e.target.value)},pattern:v},y)),"password"!==a||f?null:l().createElement("button",{className:tL(m("toggleButton"),"absolute inset-y-0 right-0 flex items-center px-2.5 rounded-lg"),type:"button",onClick:()=>j(),"aria-label":C?"Hide password":"Show Password"},C?l().createElement(ri,{className:tL("flex-none h-5 w-5 transition","text-tremor-content-subtle hover:text-tremor-content","dark:text-dark-tremor-content-subtle hover:dark:text-dark-tremor-content"),"aria-hidden":!0}):l().createElement(rl,{className:tL("flex-none h-5 w-5 transition","text-tremor-content-subtle hover:text-tremor-content","dark:text-dark-tremor-content-subtle hover:dark:text-dark-tremor-content"),"aria-hidden":!0})),d?l().createElement(ro,{className:tL(m("errorIcon"),"text-red-500 shrink-0 h-5 w-5 absolute right-0 flex items-center","password"===a?"mr-10":"number"===a?p?"mr-20":"mr-3":"mx-2.5")}):null,null!=p?p:null),d&&u?l().createElement("p",{className:tL(m("errorMessage"),"text-sm text-red-500 mt-1")},u):null)});rc.displayName="BaseInput";let rd=tV("TextInput"),ru=l().forwardRef((e,t)=>{let{type:r="text"}=e,n=i(e,["type"]);return l().createElement(rc,Object.assign({ref:t,type:r,makeInputClassName:rd},n))});ru.displayName="TextInput";let rf=l().forwardRef((e,t)=>{let{color:r,children:n,className:o}=e,a=i(e,["color","children","className"]);return l().createElement("p",Object.assign({ref:t,className:tL("font-medium text-tremor-title",r?tG(r,tA.darkText).textColor:"text-tremor-content-strong dark:text-dark-tremor-content-strong",o)},a),n)});function rp(){let[e,t]=(0,o.useState)([]),[r,l]=(0,o.useState)(!0),[i,a]=(0,o.useState)(null),[s,c]=(0,o.useState)(""),[d,u]=(0,o.useState)(null),f=e.filter(e=>e.short_title.toLowerCase().includes(s.toLowerCase())||e.description.toLowerCase().includes(s.toLowerCase())||e.policy_id.toString().includes(s));return r?n.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[n.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),n.jsx(rn,{children:"Loading policies..."})]})}):i?n.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsxs)(rn,{className:"text-red-600 mb-4",children:["Error: ",i]}),n.jsx(t9,{onClick:()=>window.location.reload(),children:"Retry"})]})}):d?n.jsx("div",{className:"min-h-screen bg-gray-50 p-4",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[n.jsx("div",{className:"mb-4",children:n.jsx(t9,{onClick:()=>u(null),children:"← Back to Dashboard"})}),n.jsx(rt,{children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)(tW,{color:"blue",children:["Policy #",d.policy_id]}),n.jsx(rf,{className:"text-2xl mt-2",children:d.short_title})]}),(0,n.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,n.jsxs)(rt,{children:[n.jsx(rn,{children:"Perplexity Length"}),n.jsx(rr,{children:d.source_analysis.perplexity_content_length})]}),(0,n.jsxs)(rt,{children:[n.jsx(rn,{children:"RAG Length"}),n.jsx(rr,{children:d.source_analysis.rag_content_length})]}),(0,n.jsxs)(rt,{children:[n.jsx(rn,{children:"Citations"}),n.jsx(rr,{children:d.source_analysis.citations_found})]})]}),(0,n.jsxs)("div",{children:[n.jsx(rn,{className:"font-semibold",children:"Description:"}),n.jsx(rn,{className:"mt-2",children:d.description})]}),(0,n.jsxs)("div",{children:[n.jsx(rn,{className:"font-semibold",children:"Original Title:"}),n.jsx(rn,{className:"mt-2 text-sm text-gray-600",children:d.policy_title})]}),(0,n.jsxs)("div",{children:[n.jsx(rn,{className:"font-semibold",children:"Perplexity Analysis:"}),n.jsx(rn,{className:"mt-2 text-sm",children:d.source_analysis.perplexity_description})]}),(0,n.jsxs)("div",{children:[n.jsx(rn,{className:"font-semibold",children:"RAG Context:"}),n.jsx(rn,{className:"mt-2 text-sm",children:d.source_analysis.rag_description})]})]})})]})}):n.jsx("div",{className:"min-h-screen bg-gray-50",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:[(0,n.jsxs)("div",{className:"mb-8",children:[n.jsx(rf,{className:"text-3xl font-bold mb-2",children:"Project 2025 Policy Dashboard"}),n.jsx(rn,{className:"text-gray-600",children:"Interactive dashboard for viewing and analyzing processed policies"}),(0,n.jsxs)("div",{className:"flex gap-2 mt-4",children:[(0,n.jsxs)(tW,{color:"blue",children:[e.length," Policies Processed"]}),n.jsx(tW,{color:"emerald",children:"Enhanced RAG v2"}),n.jsx(tW,{color:"purple",children:"Short Titles"})]})]}),n.jsx("div",{className:"mb-6",children:n.jsx(ru,{placeholder:"Search policies by ID, title, or description...",value:s,onValueChange:c,className:"max-w-md"})}),n.jsx("div",{className:"mb-4",children:(0,n.jsxs)(rn,{children:["Showing ",f.length," of ",e.length," policies"]})}),n.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:f.map(e=>n.jsx(rt,{className:"cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>u(e),children:(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(tW,{color:"blue",children:["#",e.policy_id]}),n.jsx(rn,{className:"text-xs text-gray-500",children:new Date(e.processing_metadata.processing_timestamp).toLocaleDateString()})]}),n.jsx(rf,{className:"text-lg",children:e.short_title}),n.jsx(rn,{className:"text-sm text-gray-600 line-clamp-2",children:e.description}),(0,n.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-center",children:[(0,n.jsxs)("div",{children:[n.jsx(rr,{className:"text-sm",children:e.source_analysis.perplexity_content_length}),n.jsx(rn,{className:"text-xs",children:"Perplexity"})]}),(0,n.jsxs)("div",{children:[n.jsx(rr,{className:"text-sm",children:e.source_analysis.rag_content_length}),n.jsx(rn,{className:"text-xs",children:"RAG"})]}),(0,n.jsxs)("div",{children:[n.jsx(rr,{className:"text-sm",children:e.source_analysis.citations_found}),n.jsx(rn,{className:"text-xs",children:"Citations"})]})]})]})},e.policy_id))}),0===f.length&&n.jsx("div",{className:"text-center py-12",children:n.jsx(rn,{className:"text-gray-500",children:"No policies found matching your search criteria."})})]})})}rf.displayName="Title"},1506:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>o});var n=r(9510);r(7272);let o={title:"Project 2025 Policy Dashboard",description:"Interactive dashboard for Project 2025 policy analysis"};function l({children:e}){return n.jsx("html",{lang:"en",children:n.jsx("body",{children:e})})}},908:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(8570).createProxy)(String.raw`/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx#default`)},7272:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[276,471],()=>r(4776));module.exports=n})();