/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/digital/p25/enrich/dashboard/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGamVhbi1wYXRyaWNrc21pdGglMkZkaWdpdGFsJTJGcDI1JTJGZW5yaWNoJTJGZGFzaGJvYXJkJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUF1RyIsInNvdXJjZXMiOlsid2VicGFjazovL3AyNS1kYXNoYm9hcmQvPzA2OGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvamVhbi1wYXRyaWNrc21pdGgvZGlnaXRhbC9wMjUvZW5yaWNoL2Rhc2hib2FyZC9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Metric,Text,TextInput,Title!=!@tremor/react */ \"(ssr)/__barrel_optimize__?names=Badge,Button,Card,Metric,Text,TextInput,Title!=!./node_modules/@tremor/react/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Dashboard() {\n    const [policies, setPolicies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedPolicy, setSelectedPolicy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchPolicies = async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"/api/policies\");\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch policies\");\n                }\n                const data = await response.json();\n                setPolicies(data);\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"An error occurred\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchPolicies();\n    }, []);\n    const filteredPolicies = policies.filter((policy)=>policy.short_title.toLowerCase().includes(searchTerm.toLowerCase()) || policy.description.toLowerCase().includes(searchTerm.toLowerCase()) || policy.policy_id.toString().includes(searchTerm));\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        children: \"Loading policies...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        className: \"text-red-600 mb-4\",\n                        children: [\n                            \"Error: \",\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this);\n    }\n    if (selectedPolicy) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setSelectedPolicy(null),\n                            children: \"← Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                            color: \"blue\",\n                                            children: [\n                                                \"Policy #\",\n                                                selectedPolicy.policy_id\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Title, {\n                                            className: \"text-2xl mt-2\",\n                                            children: selectedPolicy.short_title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                    children: \"Perplexity Length\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Metric, {\n                                                    children: selectedPolicy.source_analysis.perplexity_content_length\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                    children: \"RAG Length\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Metric, {\n                                                    children: selectedPolicy.source_analysis.rag_content_length\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                    children: \"Citations\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Metric, {\n                                                    children: selectedPolicy.source_analysis.citations_found\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            className: \"font-semibold\",\n                                            children: \"Description:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            className: \"mt-2\",\n                                            children: selectedPolicy.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            className: \"font-semibold\",\n                                            children: \"Original Title:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            className: \"mt-2 text-sm text-gray-600\",\n                                            children: selectedPolicy.policy_title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            className: \"font-semibold\",\n                                            children: \"Perplexity Analysis:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            className: \"mt-2 text-sm whitespace-pre-line\",\n                                            children: selectedPolicy.source_analysis.perplexity_description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            className: \"font-semibold\",\n                                            children: \"RAG Context:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            className: \"mt-2 text-sm whitespace-pre-line\",\n                                            children: selectedPolicy.source_analysis.rag_description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Title, {\n                            className: \"text-3xl font-bold mb-2\",\n                            children: \"Project 2025 Policy Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                            className: \"text-gray-600\",\n                            children: \"Interactive dashboard for viewing and analyzing processed policies\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                    color: \"blue\",\n                                    children: [\n                                        policies.length,\n                                        \" Policies Processed\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                    color: \"emerald\",\n                                    children: \"Enhanced RAG v2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                    color: \"purple\",\n                                    children: \"Short Titles\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.TextInput, {\n                        placeholder: \"Search policies by ID, title, or description...\",\n                        value: searchTerm,\n                        onValueChange: setSearchTerm,\n                        className: \"max-w-md\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        children: [\n                            \"Showing \",\n                            filteredPolicies.length,\n                            \" of \",\n                            policies.length,\n                            \" policies\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                    children: filteredPolicies.map((policy)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"cursor-pointer hover:shadow-lg transition-shadow\",\n                            onClick: ()=>setSelectedPolicy(policy),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                color: \"blue\",\n                                                children: [\n                                                    \"#\",\n                                                    policy.policy_id\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                className: \"text-xs text-gray-500\",\n                                                children: new Date(policy.processing_metadata.processing_timestamp).toLocaleDateString()\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Title, {\n                                        className: \"text-lg\",\n                                        children: policy.short_title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                        className: \"text-sm text-gray-600 line-clamp-2\",\n                                        children: policy.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-2 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Metric, {\n                                                        className: \"text-sm\",\n                                                        children: policy.source_analysis.perplexity_content_length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                        className: \"text-xs\",\n                                                        children: \"Perplexity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Metric, {\n                                                        className: \"text-sm\",\n                                                        children: policy.source_analysis.rag_content_length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                        className: \"text-xs\",\n                                                        children: \"RAG\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Metric, {\n                                                        className: \"text-sm\",\n                                                        children: policy.source_analysis.citations_found\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                        className: \"text-xs\",\n                                                        children: \"Citations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        }, policy.policy_id, false, {\n                            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this),\n                filteredPolicies.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Metric_Text_TextInput_Title_tremor_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        className: \"text-gray-500\",\n                        children: \"No policies found matching your search criteria.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=Badge,Button,Card,Metric,Text,TextInput,Title!=!./node_modules/@tremor/react/dist/index.js":
/*!****************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Badge,Button,Card,Metric,Text,TextInput,Title!=!./node_modules/@tremor/react/dist/index.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* reexport safe */ _components_icon_elements_Badge_Badge_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Button: () => (/* reexport safe */ _components_input_elements_Button_Button_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Card: () => (/* reexport safe */ _components_layout_elements_Card_Card_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Metric: () => (/* reexport safe */ _components_text_elements_Metric_Metric_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Text: () => (/* reexport safe */ _components_text_elements_Text_Text_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   TextInput: () => (/* reexport safe */ _components_input_elements_TextInput_TextInput_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   Title: () => (/* reexport safe */ _components_text_elements_Title_Title_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _components_icon_elements_Badge_Badge_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components/icon-elements/Badge/Badge.js */ \"(ssr)/./node_modules/@tremor/react/dist/components/icon-elements/Badge/Badge.js\");\n/* harmony import */ var _components_input_elements_Button_Button_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/input-elements/Button/Button.js */ \"(ssr)/./node_modules/@tremor/react/dist/components/input-elements/Button/Button.js\");\n/* harmony import */ var _components_layout_elements_Card_Card_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/layout-elements/Card/Card.js */ \"(ssr)/./node_modules/@tremor/react/dist/components/layout-elements/Card/Card.js\");\n/* harmony import */ var _components_text_elements_Metric_Metric_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/text-elements/Metric/Metric.js */ \"(ssr)/./node_modules/@tremor/react/dist/components/text-elements/Metric/Metric.js\");\n/* harmony import */ var _components_text_elements_Text_Text_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/text-elements/Text/Text.js */ \"(ssr)/./node_modules/@tremor/react/dist/components/text-elements/Text/Text.js\");\n/* harmony import */ var _components_input_elements_TextInput_TextInput_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/input-elements/TextInput/TextInput.js */ \"(ssr)/./node_modules/@tremor/react/dist/components/input-elements/TextInput/TextInput.js\");\n/* harmony import */ var _components_text_elements_Title_Title_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/text-elements/Title/Title.js */ \"(ssr)/./node_modules/@tremor/react/dist/components/text-elements/Title/Title.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYWRnZSxCdXR0b24sQ2FyZCxNZXRyaWMsVGV4dCxUZXh0SW5wdXQsVGl0bGUhPSEuL25vZGVfbW9kdWxlcy9AdHJlbW9yL3JlYWN0L2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNEU7QUFDSTtBQUNMO0FBQ0k7QUFDTjtBQUNnQjtBQUNiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcDI1LWRhc2hib2FyZC8uL25vZGVfbW9kdWxlcy9AdHJlbW9yL3JlYWN0L2Rpc3QvaW5kZXguanM/OTcwYyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQmFkZ2UgfSBmcm9tIFwiLi9jb21wb25lbnRzL2ljb24tZWxlbWVudHMvQmFkZ2UvQmFkZ2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCdXR0b24gfSBmcm9tIFwiLi9jb21wb25lbnRzL2lucHV0LWVsZW1lbnRzL0J1dHRvbi9CdXR0b24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDYXJkIH0gZnJvbSBcIi4vY29tcG9uZW50cy9sYXlvdXQtZWxlbWVudHMvQ2FyZC9DYXJkLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWV0cmljIH0gZnJvbSBcIi4vY29tcG9uZW50cy90ZXh0LWVsZW1lbnRzL01ldHJpYy9NZXRyaWMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUZXh0IH0gZnJvbSBcIi4vY29tcG9uZW50cy90ZXh0LWVsZW1lbnRzL1RleHQvVGV4dC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRleHRJbnB1dCB9IGZyb20gXCIuL2NvbXBvbmVudHMvaW5wdXQtZWxlbWVudHMvVGV4dElucHV0L1RleHRJbnB1dC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRpdGxlIH0gZnJvbSBcIi4vY29tcG9uZW50cy90ZXh0LWVsZW1lbnRzL1RpdGxlL1RpdGxlLmpzXCIiXSwibmFtZXMiOlsiZGVmYXVsdCIsIkJhZGdlIiwiQnV0dG9uIiwiQ2FyZCIsIk1ldHJpYyIsIlRleHQiLCJUZXh0SW5wdXQiLCJUaXRsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=Badge,Button,Card,Metric,Text,TextInput,Title!=!./node_modules/@tremor/react/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"205a5349122c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wMjUtZGFzaGJvYXJkLy4vYXBwL2dsb2JhbHMuY3NzPzZkNDUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyMDVhNTM0OTEyMmNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"Project 2025 Policy Dashboard\",\n    description: \"Interactive dashboard for Project 2025 policy analysis\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/digital/p25/enrich/dashboard/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDc0I7QUFFZixNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFBTUg7Ozs7Ozs7Ozs7O0FBR2IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wMjUtZGFzaGJvYXJkLy4vYXBwL2xheW91dC50c3g/OTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdQcm9qZWN0IDIwMjUgUG9saWN5IERhc2hib2FyZCcsXG4gIGRlc2NyaXB0aW9uOiAnSW50ZXJhY3RpdmUgZGFzaGJvYXJkIGZvciBQcm9qZWN0IDIwMjUgcG9saWN5IGFuYWx5c2lzJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keT57Y2hpbGRyZW59PC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufSJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/digital/p25/enrich/dashboard/app/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@tremor","vendor-chunks/@floating-ui","vendor-chunks/react-transition-state","vendor-chunks/tslib","vendor-chunks/tailwind-merge","vendor-chunks/tabbable","vendor-chunks/aria-hidden"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjean-patricksmith%2Fdigital%2Fp25%2Fenrich%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();