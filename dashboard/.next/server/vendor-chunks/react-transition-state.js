"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-transition-state";
exports.ids = ["vendor-chunks/react-transition-state"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-transition-state/dist/esm/hooks/useTransitionState.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/react-transition-state/dist/esm/hooks/useTransitionState.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTransitionState: () => (/* binding */ useTransitionState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/react-transition-state/dist/esm/hooks/utils.mjs\");\n\n\n\nconst updateState = (status, setState, latestState, timeoutId, onChange) => {\n  clearTimeout(timeoutId.current);\n  const state = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.getState)(status);\n  setState(state);\n  latestState.current = state;\n  onChange && onChange({\n    current: state\n  });\n};\nconst useTransitionState = ({\n  enter = true,\n  exit = true,\n  preEnter,\n  preExit,\n  timeout,\n  initialEntered,\n  mountOnEnter,\n  unmountOnExit,\n  onStateChange: onChange\n} = {}) => {\n  const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.getState)(initialEntered ? _utils_mjs__WEBPACK_IMPORTED_MODULE_1__.ENTERED : (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.startOrEnd)(mountOnEnter)));\n  const latestState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(state);\n  const timeoutId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [enterTimeout, exitTimeout] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.getTimeout)(timeout);\n  const endTransition = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const status = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.getEndStatus)(latestState.current._s, unmountOnExit);\n    status && updateState(status, setState, latestState, timeoutId, onChange);\n  }, [onChange, unmountOnExit]);\n  const toggle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(toEnter => {\n    const transitState = status => {\n      updateState(status, setState, latestState, timeoutId, onChange);\n      switch (status) {\n        case _utils_mjs__WEBPACK_IMPORTED_MODULE_1__.ENTERING:\n          if (enterTimeout >= 0) timeoutId.current = setTimeout(endTransition, enterTimeout);\n          break;\n        case _utils_mjs__WEBPACK_IMPORTED_MODULE_1__.EXITING:\n          if (exitTimeout >= 0) timeoutId.current = setTimeout(endTransition, exitTimeout);\n          break;\n        case _utils_mjs__WEBPACK_IMPORTED_MODULE_1__.PRE_ENTER:\n        case _utils_mjs__WEBPACK_IMPORTED_MODULE_1__.PRE_EXIT:\n          timeoutId.current = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.nextTick)(transitState, status);\n          break;\n      }\n    };\n    const enterStage = latestState.current.isEnter;\n    if (typeof toEnter !== 'boolean') toEnter = !enterStage;\n    if (toEnter) {\n      !enterStage && transitState(enter ? preEnter ? _utils_mjs__WEBPACK_IMPORTED_MODULE_1__.PRE_ENTER : _utils_mjs__WEBPACK_IMPORTED_MODULE_1__.ENTERING : _utils_mjs__WEBPACK_IMPORTED_MODULE_1__.ENTERED);\n    } else {\n      enterStage && transitState(exit ? preExit ? _utils_mjs__WEBPACK_IMPORTED_MODULE_1__.PRE_EXIT : _utils_mjs__WEBPACK_IMPORTED_MODULE_1__.EXITING : (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.startOrEnd)(unmountOnExit));\n    }\n  }, [endTransition, onChange, enter, exit, preEnter, preExit, enterTimeout, exitTimeout, unmountOnExit]);\n  return [state, toggle, endTransition];\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-transition-state/dist/esm/hooks/useTransitionState.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-transition-state/dist/esm/hooks/utils.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/react-transition-state/dist/esm/hooks/utils.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ENTERED: () => (/* binding */ ENTERED),\n/* harmony export */   ENTERING: () => (/* binding */ ENTERING),\n/* harmony export */   EXITED: () => (/* binding */ EXITED),\n/* harmony export */   EXITING: () => (/* binding */ EXITING),\n/* harmony export */   PRE_ENTER: () => (/* binding */ PRE_ENTER),\n/* harmony export */   PRE_EXIT: () => (/* binding */ PRE_EXIT),\n/* harmony export */   STATUS: () => (/* binding */ STATUS),\n/* harmony export */   UNMOUNTED: () => (/* binding */ UNMOUNTED),\n/* harmony export */   getEndStatus: () => (/* binding */ getEndStatus),\n/* harmony export */   getState: () => (/* binding */ getState),\n/* harmony export */   getTimeout: () => (/* binding */ getTimeout),\n/* harmony export */   nextTick: () => (/* binding */ nextTick),\n/* harmony export */   startOrEnd: () => (/* binding */ startOrEnd)\n/* harmony export */ });\nconst PRE_ENTER = 0;\nconst ENTERING = 1;\nconst ENTERED = 2;\nconst PRE_EXIT = 3;\nconst EXITING = 4;\nconst EXITED = 5;\nconst UNMOUNTED = 6;\nconst STATUS = ['preEnter', 'entering', 'entered', 'preExit', 'exiting', 'exited', 'unmounted'];\nconst getState = status => ({\n  _s: status,\n  status: STATUS[status],\n  isEnter: status < PRE_EXIT,\n  isMounted: status !== UNMOUNTED,\n  isResolved: status === ENTERED || status > EXITING\n});\nconst startOrEnd = unmounted => unmounted ? UNMOUNTED : EXITED;\nconst getEndStatus = (status, unmountOnExit) => {\n  switch (status) {\n    case ENTERING:\n    case PRE_ENTER:\n      return ENTERED;\n    case EXITING:\n    case PRE_EXIT:\n      return startOrEnd(unmountOnExit);\n  }\n};\nconst getTimeout = timeout => typeof timeout === 'object' ? [timeout.enter, timeout.exit] : [timeout, timeout];\nconst nextTick = (transitState, status) => setTimeout(() => {\n  // Reading document.body.offsetTop can force browser to repaint before transition to the next state\n  isNaN(document.body.offsetTop) || transitState(status + 1);\n}, 0);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtdHJhbnNpdGlvbi1zdGF0ZS9kaXN0L2VzbS9ob29rcy91dGlscy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFK0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wMjUtZGFzaGJvYXJkLy4vbm9kZV9tb2R1bGVzL3JlYWN0LXRyYW5zaXRpb24tc3RhdGUvZGlzdC9lc20vaG9va3MvdXRpbHMubWpzPzQyMWQiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgUFJFX0VOVEVSID0gMDtcbmNvbnN0IEVOVEVSSU5HID0gMTtcbmNvbnN0IEVOVEVSRUQgPSAyO1xuY29uc3QgUFJFX0VYSVQgPSAzO1xuY29uc3QgRVhJVElORyA9IDQ7XG5jb25zdCBFWElURUQgPSA1O1xuY29uc3QgVU5NT1VOVEVEID0gNjtcbmNvbnN0IFNUQVRVUyA9IFsncHJlRW50ZXInLCAnZW50ZXJpbmcnLCAnZW50ZXJlZCcsICdwcmVFeGl0JywgJ2V4aXRpbmcnLCAnZXhpdGVkJywgJ3VubW91bnRlZCddO1xuY29uc3QgZ2V0U3RhdGUgPSBzdGF0dXMgPT4gKHtcbiAgX3M6IHN0YXR1cyxcbiAgc3RhdHVzOiBTVEFUVVNbc3RhdHVzXSxcbiAgaXNFbnRlcjogc3RhdHVzIDwgUFJFX0VYSVQsXG4gIGlzTW91bnRlZDogc3RhdHVzICE9PSBVTk1PVU5URUQsXG4gIGlzUmVzb2x2ZWQ6IHN0YXR1cyA9PT0gRU5URVJFRCB8fCBzdGF0dXMgPiBFWElUSU5HXG59KTtcbmNvbnN0IHN0YXJ0T3JFbmQgPSB1bm1vdW50ZWQgPT4gdW5tb3VudGVkID8gVU5NT1VOVEVEIDogRVhJVEVEO1xuY29uc3QgZ2V0RW5kU3RhdHVzID0gKHN0YXR1cywgdW5tb3VudE9uRXhpdCkgPT4ge1xuICBzd2l0Y2ggKHN0YXR1cykge1xuICAgIGNhc2UgRU5URVJJTkc6XG4gICAgY2FzZSBQUkVfRU5URVI6XG4gICAgICByZXR1cm4gRU5URVJFRDtcbiAgICBjYXNlIEVYSVRJTkc6XG4gICAgY2FzZSBQUkVfRVhJVDpcbiAgICAgIHJldHVybiBzdGFydE9yRW5kKHVubW91bnRPbkV4aXQpO1xuICB9XG59O1xuY29uc3QgZ2V0VGltZW91dCA9IHRpbWVvdXQgPT4gdHlwZW9mIHRpbWVvdXQgPT09ICdvYmplY3QnID8gW3RpbWVvdXQuZW50ZXIsIHRpbWVvdXQuZXhpdF0gOiBbdGltZW91dCwgdGltZW91dF07XG5jb25zdCBuZXh0VGljayA9ICh0cmFuc2l0U3RhdGUsIHN0YXR1cykgPT4gc2V0VGltZW91dCgoKSA9PiB7XG4gIC8vIFJlYWRpbmcgZG9jdW1lbnQuYm9keS5vZmZzZXRUb3AgY2FuIGZvcmNlIGJyb3dzZXIgdG8gcmVwYWludCBiZWZvcmUgdHJhbnNpdGlvbiB0byB0aGUgbmV4dCBzdGF0ZVxuICBpc05hTihkb2N1bWVudC5ib2R5Lm9mZnNldFRvcCkgfHwgdHJhbnNpdFN0YXRlKHN0YXR1cyArIDEpO1xufSwgMCk7XG5cbmV4cG9ydCB7IEVOVEVSRUQsIEVOVEVSSU5HLCBFWElURUQsIEVYSVRJTkcsIFBSRV9FTlRFUiwgUFJFX0VYSVQsIFNUQVRVUywgVU5NT1VOVEVELCBnZXRFbmRTdGF0dXMsIGdldFN0YXRlLCBnZXRUaW1lb3V0LCBuZXh0VGljaywgc3RhcnRPckVuZCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-transition-state/dist/esm/hooks/utils.mjs\n");

/***/ })

};
;