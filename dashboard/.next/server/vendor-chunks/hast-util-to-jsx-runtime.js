"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-jsx-runtime";
exports.ids = ["vendor-chunks/hast-util-to-jsx-runtime"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-to-jsx-runtime/lib/index.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-jsx-runtime/lib/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toJsxRuntime: () => (/* binding */ toJsxRuntime)\n/* harmony export */ });\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! estree-util-is-identifier-name */ \"(ssr)/./node_modules/estree-util-is-identifier-name/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/hast-to-react.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var style_to_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! style-to-js */ \"(ssr)/./node_modules/style-to-js/cjs/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/lib/index.js\");\n/**\n * @import {Identifier, Literal, MemberExpression} from 'estree'\n * @import {Jsx, JsxDev, Options, Props} from 'hast-util-to-jsx-runtime'\n * @import {Element, Nodes, Parents, Root, Text} from 'hast'\n * @import {MdxFlowExpressionHast, MdxTextExpressionHast} from 'mdast-util-mdx-expression'\n * @import {MdxJsxFlowElementHast, MdxJsxTextElementHast} from 'mdast-util-mdx-jsx'\n * @import {MdxjsEsmHast} from 'mdast-util-mdxjs-esm'\n * @import {Position} from 'unist'\n * @import {Child, Create, Field, JsxElement, State, Style} from './types.js'\n */\n\n\n\n\n\n\n\n\n\n\n\n// To do: next major: `Object.hasOwn`.\nconst own = {}.hasOwnProperty\n\n/** @type {Map<string, number>} */\nconst emptyMap = new Map()\n\nconst cap = /[A-Z]/g\n\n// `react-dom` triggers a warning for *any* white space in tables.\n// To follow GFM, `mdast-util-to-hast` injects line endings between elements.\n// Other tools might do so too, but they don’t do here, so we remove all of\n// that.\n\n// See: <https://github.com/facebook/react/pull/7081>.\n// See: <https://github.com/facebook/react/pull/7515>.\n// See: <https://github.com/remarkjs/remark-react/issues/64>.\n// See: <https://github.com/rehypejs/rehype-react/pull/29>.\n// See: <https://github.com/rehypejs/rehype-react/pull/32>.\n// See: <https://github.com/rehypejs/rehype-react/pull/45>.\nconst tableElements = new Set(['table', 'tbody', 'thead', 'tfoot', 'tr'])\n\nconst tableCellElement = new Set(['td', 'th'])\n\nconst docs = 'https://github.com/syntax-tree/hast-util-to-jsx-runtime'\n\n/**\n * Transform a hast tree to preact, react, solid, svelte, vue, etc.,\n * with an automatic JSX runtime.\n *\n * @param {Nodes} tree\n *   Tree to transform.\n * @param {Options} options\n *   Configuration (required).\n * @returns {JsxElement}\n *   JSX element.\n */\n\nfunction toJsxRuntime(tree, options) {\n  if (!options || options.Fragment === undefined) {\n    throw new TypeError('Expected `Fragment` in options')\n  }\n\n  const filePath = options.filePath || undefined\n  /** @type {Create} */\n  let create\n\n  if (options.development) {\n    if (typeof options.jsxDEV !== 'function') {\n      throw new TypeError(\n        'Expected `jsxDEV` in options when `development: true`'\n      )\n    }\n\n    create = developmentCreate(filePath, options.jsxDEV)\n  } else {\n    if (typeof options.jsx !== 'function') {\n      throw new TypeError('Expected `jsx` in production options')\n    }\n\n    if (typeof options.jsxs !== 'function') {\n      throw new TypeError('Expected `jsxs` in production options')\n    }\n\n    create = productionCreate(filePath, options.jsx, options.jsxs)\n  }\n\n  /** @type {State} */\n  const state = {\n    Fragment: options.Fragment,\n    ancestors: [],\n    components: options.components || {},\n    create,\n    elementAttributeNameCase: options.elementAttributeNameCase || 'react',\n    evaluater: options.createEvaluater ? options.createEvaluater() : undefined,\n    filePath,\n    ignoreInvalidStyle: options.ignoreInvalidStyle || false,\n    passKeys: options.passKeys !== false,\n    passNode: options.passNode || false,\n    schema: options.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_1__.svg : property_information__WEBPACK_IMPORTED_MODULE_1__.html,\n    stylePropertyNameCase: options.stylePropertyNameCase || 'dom',\n    tableCellAlignToStyle: options.tableCellAlignToStyle !== false\n  }\n\n  const result = one(state, tree, undefined)\n\n  // JSX element.\n  if (result && typeof result !== 'string') {\n    return result\n  }\n\n  // Text node or something that turned into nothing.\n  return state.create(\n    tree,\n    state.Fragment,\n    {children: result || undefined},\n    undefined\n  )\n}\n\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Nodes} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction one(state, node, key) {\n  if (node.type === 'element') {\n    return element(state, node, key)\n  }\n\n  if (node.type === 'mdxFlowExpression' || node.type === 'mdxTextExpression') {\n    return mdxExpression(state, node)\n  }\n\n  if (node.type === 'mdxJsxFlowElement' || node.type === 'mdxJsxTextElement') {\n    return mdxJsxElement(state, node, key)\n  }\n\n  if (node.type === 'mdxjsEsm') {\n    return mdxEsm(state, node)\n  }\n\n  if (node.type === 'root') {\n    return root(state, node, key)\n  }\n\n  if (node.type === 'text') {\n    return text(state, node)\n  }\n}\n\n/**\n * Handle element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction element(state, node, key) {\n  const parentSchema = state.schema\n  let schema = parentSchema\n\n  if (node.tagName.toLowerCase() === 'svg' && parentSchema.space === 'html') {\n    schema = property_information__WEBPACK_IMPORTED_MODULE_1__.svg\n    state.schema = schema\n  }\n\n  state.ancestors.push(node)\n\n  const type = findComponentFromName(state, node.tagName, false)\n  const props = createElementProps(state, node)\n  let children = createChildren(state, node)\n\n  if (tableElements.has(node.tagName)) {\n    children = children.filter(function (child) {\n      return typeof child === 'string' ? !(0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(child) : true\n    })\n  }\n\n  addNode(state, props, type, node)\n  addChildren(props, children)\n\n  // Restore.\n  state.ancestors.pop()\n  state.schema = parentSchema\n\n  return state.create(node, type, props, key)\n}\n\n/**\n * Handle MDX expression.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxFlowExpressionHast | MdxTextExpressionHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxExpression(state, node) {\n  if (node.data && node.data.estree && state.evaluater) {\n    const program = node.data.estree\n    const expression = program.body[0]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(expression.type === 'ExpressionStatement')\n\n    // Assume result is a child.\n    return /** @type {Child | undefined} */ (\n      state.evaluater.evaluateExpression(expression.expression)\n    )\n  }\n\n  crashEstree(state, node.position)\n}\n\n/**\n * Handle MDX ESM.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxjsEsmHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxEsm(state, node) {\n  if (node.data && node.data.estree && state.evaluater) {\n    // Assume result is a child.\n    return /** @type {Child | undefined} */ (\n      state.evaluater.evaluateProgram(node.data.estree)\n    )\n  }\n\n  crashEstree(state, node.position)\n}\n\n/**\n * Handle MDX JSX.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxJsxElement(state, node, key) {\n  const parentSchema = state.schema\n  let schema = parentSchema\n\n  if (node.name === 'svg' && parentSchema.space === 'html') {\n    schema = property_information__WEBPACK_IMPORTED_MODULE_1__.svg\n    state.schema = schema\n  }\n\n  state.ancestors.push(node)\n\n  const type =\n    node.name === null\n      ? state.Fragment\n      : findComponentFromName(state, node.name, true)\n  const props = createJsxElementProps(state, node)\n  const children = createChildren(state, node)\n\n  addNode(state, props, type, node)\n  addChildren(props, children)\n\n  // Restore.\n  state.ancestors.pop()\n  state.schema = parentSchema\n\n  return state.create(node, type, props, key)\n}\n\n/**\n * Handle root.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Root} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction root(state, node, key) {\n  /** @type {Props} */\n  const props = {}\n\n  addChildren(props, createChildren(state, node))\n\n  return state.create(node, state.Fragment, props, key)\n}\n\n/**\n * Handle text.\n *\n * @param {State} _\n *   Info passed around.\n * @param {Text} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction text(_, node) {\n  return node.value\n}\n\n/**\n * Add `node` to props.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Props} props\n *   Props.\n * @param {unknown} type\n *   Type.\n * @param {Element | MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addNode(state, props, type, node) {\n  // If this is swapped out for a component:\n  if (typeof type !== 'string' && type !== state.Fragment && state.passNode) {\n    props.node = node\n  }\n}\n\n/**\n * Add children to props.\n *\n * @param {Props} props\n *   Props.\n * @param {Array<Child>} children\n *   Children.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addChildren(props, children) {\n  if (children.length > 0) {\n    const value = children.length > 1 ? children : children[0]\n\n    if (value) {\n      props.children = value\n    }\n  }\n}\n\n/**\n * @param {string | undefined} _\n *   Path to file.\n * @param {Jsx} jsx\n *   Dynamic.\n * @param {Jsx} jsxs\n *   Static.\n * @returns {Create}\n *   Create a production element.\n */\nfunction productionCreate(_, jsx, jsxs) {\n  return create\n  /** @type {Create} */\n  function create(_, type, props, key) {\n    // Only an array when there are 2 or more children.\n    const isStaticChildren = Array.isArray(props.children)\n    const fn = isStaticChildren ? jsxs : jsx\n    return key ? fn(type, props, key) : fn(type, props)\n  }\n}\n\n/**\n * @param {string | undefined} filePath\n *   Path to file.\n * @param {JsxDev} jsxDEV\n *   Development.\n * @returns {Create}\n *   Create a development element.\n */\nfunction developmentCreate(filePath, jsxDEV) {\n  return create\n  /** @type {Create} */\n  function create(node, type, props, key) {\n    // Only an array when there are 2 or more children.\n    const isStaticChildren = Array.isArray(props.children)\n    const point = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_4__.pointStart)(node)\n    return jsxDEV(\n      type,\n      props,\n      key,\n      isStaticChildren,\n      {\n        columnNumber: point ? point.column - 1 : undefined,\n        fileName: filePath,\n        lineNumber: point ? point.line : undefined\n      },\n      undefined\n    )\n  }\n}\n\n/**\n * Create props from an element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current element.\n * @returns {Props}\n *   Props.\n */\nfunction createElementProps(state, node) {\n  /** @type {Props} */\n  const props = {}\n  /** @type {string | undefined} */\n  let alignValue\n  /** @type {string} */\n  let prop\n\n  for (prop in node.properties) {\n    if (prop !== 'children' && own.call(node.properties, prop)) {\n      const result = createProperty(state, prop, node.properties[prop])\n\n      if (result) {\n        const [key, value] = result\n\n        if (\n          state.tableCellAlignToStyle &&\n          key === 'align' &&\n          typeof value === 'string' &&\n          tableCellElement.has(node.tagName)\n        ) {\n          alignValue = value\n        } else {\n          props[key] = value\n        }\n      }\n    }\n  }\n\n  if (alignValue) {\n    // Assume style is an object.\n    const style = /** @type {Style} */ (props.style || (props.style = {}))\n    style[state.stylePropertyNameCase === 'css' ? 'text-align' : 'textAlign'] =\n      alignValue\n  }\n\n  return props\n}\n\n/**\n * Create props from a JSX element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current JSX element.\n * @returns {Props}\n *   Props.\n */\nfunction createJsxElementProps(state, node) {\n  /** @type {Props} */\n  const props = {}\n\n  for (const attribute of node.attributes) {\n    if (attribute.type === 'mdxJsxExpressionAttribute') {\n      if (attribute.data && attribute.data.estree && state.evaluater) {\n        const program = attribute.data.estree\n        const expression = program.body[0]\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(expression.type === 'ExpressionStatement')\n        const objectExpression = expression.expression\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(objectExpression.type === 'ObjectExpression')\n        const property = objectExpression.properties[0]\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(property.type === 'SpreadElement')\n\n        Object.assign(\n          props,\n          state.evaluater.evaluateExpression(property.argument)\n        )\n      } else {\n        crashEstree(state, node.position)\n      }\n    } else {\n      // For JSX, the author is responsible of passing in the correct values.\n      const name = attribute.name\n      /** @type {unknown} */\n      let value\n\n      if (attribute.value && typeof attribute.value === 'object') {\n        if (\n          attribute.value.data &&\n          attribute.value.data.estree &&\n          state.evaluater\n        ) {\n          const program = attribute.value.data.estree\n          const expression = program.body[0]\n          ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(expression.type === 'ExpressionStatement')\n          value = state.evaluater.evaluateExpression(expression.expression)\n        } else {\n          crashEstree(state, node.position)\n        }\n      } else {\n        value = attribute.value === null ? true : attribute.value\n      }\n\n      // Assume a prop.\n      props[name] = /** @type {Props[keyof Props]} */ (value)\n    }\n  }\n\n  return props\n}\n\n/**\n * Create children.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Parents} node\n *   Current element.\n * @returns {Array<Child>}\n *   Children.\n */\nfunction createChildren(state, node) {\n  /** @type {Array<Child>} */\n  const children = []\n  let index = -1\n  /** @type {Map<string, number>} */\n  // Note: test this when Solid doesn’t want to merge my upcoming PR.\n  /* c8 ignore next */\n  const countsByName = state.passKeys ? new Map() : emptyMap\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n    /** @type {string | undefined} */\n    let key\n\n    if (state.passKeys) {\n      const name =\n        child.type === 'element'\n          ? child.tagName\n          : child.type === 'mdxJsxFlowElement' ||\n              child.type === 'mdxJsxTextElement'\n            ? child.name\n            : undefined\n\n      if (name) {\n        const count = countsByName.get(name) || 0\n        key = name + '-' + count\n        countsByName.set(name, count + 1)\n      }\n    }\n\n    const result = one(state, child, key)\n    if (result !== undefined) children.push(result)\n  }\n\n  return children\n}\n\n/**\n * Handle a property.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} prop\n *   Key.\n * @param {Array<number | string> | boolean | number | string | null | undefined} value\n *   hast property value.\n * @returns {Field | undefined}\n *   Field for runtime, optional.\n */\nfunction createProperty(state, prop, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_5__.find)(state.schema, prop)\n\n  // Ignore nullish and `NaN` values.\n  if (\n    value === null ||\n    value === undefined ||\n    (typeof value === 'number' && Number.isNaN(value))\n  ) {\n    return\n  }\n\n  if (Array.isArray(value)) {\n    // Accept `array`.\n    // Most props are space-separated.\n    value = info.commaSeparated ? (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_6__.stringify)(value) : (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_7__.stringify)(value)\n  }\n\n  // React only accepts `style` as object.\n  if (info.property === 'style') {\n    let styleObject =\n      typeof value === 'object' ? value : parseStyle(state, String(value))\n\n    if (state.stylePropertyNameCase === 'css') {\n      styleObject = transformStylesToCssCasing(styleObject)\n    }\n\n    return ['style', styleObject]\n  }\n\n  return [\n    state.elementAttributeNameCase === 'react' && info.space\n      ? property_information__WEBPACK_IMPORTED_MODULE_8__.hastToReact[info.property] || info.property\n      : info.attribute,\n    value\n  ]\n}\n\n/**\n * Parse a CSS declaration to an object.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} value\n *   CSS declarations.\n * @returns {Style}\n *   Properties.\n * @throws\n *   Throws `VFileMessage` when CSS cannot be parsed.\n */\nfunction parseStyle(state, value) {\n  try {\n    return style_to_js__WEBPACK_IMPORTED_MODULE_0__(value, {reactCompat: true})\n  } catch (error) {\n    if (state.ignoreInvalidStyle) {\n      return {}\n    }\n\n    const cause = /** @type {Error} */ (error)\n    const message = new vfile_message__WEBPACK_IMPORTED_MODULE_9__.VFileMessage('Cannot parse `style` attribute', {\n      ancestors: state.ancestors,\n      cause,\n      ruleId: 'style',\n      source: 'hast-util-to-jsx-runtime'\n    })\n    message.file = state.filePath || undefined\n    message.url = docs + '#cannot-parse-style-attribute'\n\n    throw message\n  }\n}\n\n/**\n * Create a JSX name from a string.\n *\n * @param {State} state\n *   To do.\n * @param {string} name\n *   Name.\n * @param {boolean} allowExpression\n *   Allow member expressions and identifiers.\n * @returns {unknown}\n *   To do.\n */\nfunction findComponentFromName(state, name, allowExpression) {\n  /** @type {Identifier | Literal | MemberExpression} */\n  let result\n\n  if (!allowExpression) {\n    result = {type: 'Literal', value: name}\n  } else if (name.includes('.')) {\n    const identifiers = name.split('.')\n    let index = -1\n    /** @type {Identifier | Literal | MemberExpression | undefined} */\n    let node\n\n    while (++index < identifiers.length) {\n      /** @type {Identifier | Literal} */\n      const prop = (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_10__.name)(identifiers[index])\n        ? {type: 'Identifier', name: identifiers[index]}\n        : {type: 'Literal', value: identifiers[index]}\n      node = node\n        ? {\n            type: 'MemberExpression',\n            object: node,\n            property: prop,\n            computed: Boolean(index && prop.type === 'Literal'),\n            optional: false\n          }\n        : prop\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(node, 'always a result')\n    result = node\n  } else {\n    result =\n      (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_10__.name)(name) && !/^[a-z]/.test(name)\n        ? {type: 'Identifier', name}\n        : {type: 'Literal', value: name}\n  }\n\n  // Only literals can be passed in `components` currently.\n  // No identifiers / member expressions.\n  if (result.type === 'Literal') {\n    const name = /** @type {string | number} */ (result.value)\n    return own.call(state.components, name) ? state.components[name] : name\n  }\n\n  // Assume component.\n  if (state.evaluater) {\n    return state.evaluater.evaluateExpression(result)\n  }\n\n  crashEstree(state)\n}\n\n/**\n * @param {State} state\n * @param {Position | undefined} [place]\n * @returns {never}\n */\nfunction crashEstree(state, place) {\n  const message = new vfile_message__WEBPACK_IMPORTED_MODULE_9__.VFileMessage(\n    'Cannot handle MDX estrees without `createEvaluater`',\n    {\n      ancestors: state.ancestors,\n      place,\n      ruleId: 'mdx-estree',\n      source: 'hast-util-to-jsx-runtime'\n    }\n  )\n  message.file = state.filePath || undefined\n  message.url = docs + '#cannot-handle-mdx-estrees-without-createevaluater'\n\n  throw message\n}\n\n/**\n * Transform a DOM casing style object to a CSS casing style object.\n *\n * @param {Style} domCasing\n * @returns {Style}\n */\nfunction transformStylesToCssCasing(domCasing) {\n  /** @type {Style} */\n  const cssCasing = {}\n  /** @type {string} */\n  let from\n\n  for (from in domCasing) {\n    if (own.call(domCasing, from)) {\n      cssCasing[transformStyleToCssCasing(from)] = domCasing[from]\n    }\n  }\n\n  return cssCasing\n}\n\n/**\n * Transform a DOM casing style field to a CSS casing style field.\n *\n * @param {string} from\n * @returns {string}\n */\nfunction transformStyleToCssCasing(from) {\n  let to = from.replace(cap, toDash)\n  // Handle `ms-xxx` -> `-ms-xxx`.\n  if (to.slice(0, 3) === 'ms-') to = '-' + to\n  return to\n}\n\n/**\n * Make `$0` dash cased.\n *\n * @param {string} $0\n *   Capitalized ASCII leter.\n * @returns {string}\n *   Dash and lower letter.\n */\nfunction toDash($0) {\n  return '-' + $0.toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-jsx-runtime/lib/index.js\n");

/***/ })

};
;