'use client';

import { useState, useEffect } from 'react';
import { Title, Text, TextInput, Card, Badge, Metric, Button } from '@tremor/react';

// Component to format long text with proper line breaks
function FormattedText({ children, className = "" }: { children: string; className?: string }) {
  const formatText = (text: string) => {
    if (!text) return '';

    // Split text into paragraphs and format
    return text
      // Add line breaks after sentences that end with periods followed by capital letters
      .replace(/\. ([A-Z])/g, '.\n\n$1')
      // Add line breaks after bullet points
      .replace(/- ([A-Z])/g, '- $1\n')
      // Add line breaks after section headers (text followed by colon)
      .replace(/([A-Za-z\s]+:)\s*([A-Z])/g, '$1\n$2')
      // Add line breaks after numbered lists
      .replace(/(\d+\.\s)/g, '\n$1')
      // Clean up multiple consecutive line breaks
      .replace(/\n{3,}/g, '\n\n')
      .trim();
  };

  const formattedText = formatText(children);

  return (
    <Text className={className}>
      {formattedText.split('\n').map((line, index) => (
        <span key={index}>
          {line}
          {index < formattedText.split('\n').length - 1 && <br />}
        </span>
      ))}
    </Text>
  );
}

interface Policy {
  policy_id: number;
  policy_title: string;
  short_title: string;
  description: string;
  source_analysis: {
    perplexity_title: string;
    perplexity_description: string;
    rag_title: string;
    rag_description: string;
    perplexity_content_length: number;
    rag_content_length: number;
    citations_found: number;
  };
  processing_metadata: {
    processing_timestamp: string;
    version: string;
  };
}

export default function Dashboard() {
  const [policies, setPolicies] = useState<Policy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);

  useEffect(() => {
    const fetchPolicies = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/policies');
        if (!response.ok) {
          throw new Error('Failed to fetch policies');
        }
        const data = await response.json();
        setPolicies(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchPolicies();
  }, []);

  const filteredPolicies = policies.filter(policy =>
    policy.short_title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    policy.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    policy.policy_id.toString().includes(searchTerm)
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <Text>Loading policies...</Text>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Text className="text-red-600 mb-4">Error: {error}</Text>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  if (selectedPolicy) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="mb-4">
            <Button onClick={() => setSelectedPolicy(null)}>← Back to Dashboard</Button>
          </div>
          <Card>
            <div className="space-y-4">
              <div>
                <Badge color="blue">Policy #{selectedPolicy.policy_id}</Badge>
                <Title className="text-2xl mt-2">{selectedPolicy.short_title}</Title>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <Card>
                  <Text>Perplexity Length</Text>
                  <Metric>{selectedPolicy.source_analysis.perplexity_content_length}</Metric>
                </Card>
                <Card>
                  <Text>RAG Length</Text>
                  <Metric>{selectedPolicy.source_analysis.rag_content_length}</Metric>
                </Card>
                <Card>
                  <Text>Citations</Text>
                  <Metric>{selectedPolicy.source_analysis.citations_found}</Metric>
                </Card>
              </div>

              <div>
                <Text className="font-semibold">Description:</Text>
                <Text className="mt-2">{selectedPolicy.description}</Text>
              </div>

              <div>
                <Text className="font-semibold">Original Title:</Text>
                <Text className="mt-2 text-sm text-gray-600">{selectedPolicy.policy_title}</Text>
              </div>

              <div>
                <Text className="font-semibold">Perplexity Analysis:</Text>
                <Text className="mt-2 text-sm">{selectedPolicy.source_analysis.perplexity_description}</Text>
              </div>

              <div>
                <Text className="font-semibold">RAG Context:</Text>
                <Text className="mt-2 text-sm">{selectedPolicy.source_analysis.rag_description}</Text>
              </div>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="mb-8">
          <Title className="text-3xl font-bold mb-2">Project 2025 Policy Dashboard</Title>
          <Text className="text-gray-600">
            Interactive dashboard for viewing and analyzing processed policies
          </Text>
          <div className="flex gap-2 mt-4">
            <Badge color="blue">{policies.length} Policies Processed</Badge>
            <Badge color="emerald">Enhanced RAG v2</Badge>
            <Badge color="purple">Short Titles</Badge>
          </div>
        </div>

        <div className="mb-6">
          <TextInput
            placeholder="Search policies by ID, title, or description..."
            value={searchTerm}
            onValueChange={setSearchTerm}
            className="max-w-md"
          />
        </div>

        <div className="mb-4">
          <Text>Showing {filteredPolicies.length} of {policies.length} policies</Text>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPolicies.map((policy) => (
            <Card
              key={policy.policy_id}
              className="cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => setSelectedPolicy(policy)}
            >
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Badge color="blue">#{policy.policy_id}</Badge>
                  <Text className="text-xs text-gray-500">
                    {new Date(policy.processing_metadata.processing_timestamp).toLocaleDateString()}
                  </Text>
                </div>

                <Title className="text-lg">{policy.short_title}</Title>

                <Text className="text-sm text-gray-600 line-clamp-2">
                  {policy.description}
                </Text>

                <div className="grid grid-cols-3 gap-2 text-center">
                  <div>
                    <Metric className="text-sm">{policy.source_analysis.perplexity_content_length}</Metric>
                    <Text className="text-xs">Perplexity</Text>
                  </div>
                  <div>
                    <Metric className="text-sm">{policy.source_analysis.rag_content_length}</Metric>
                    <Text className="text-xs">RAG</Text>
                  </div>
                  <div>
                    <Metric className="text-sm">{policy.source_analysis.citations_found}</Metric>
                    <Text className="text-xs">Citations</Text>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {filteredPolicies.length === 0 && (
          <div className="text-center py-12">
            <Text className="text-gray-500">
              No policies found matching your search criteria.
            </Text>
          </div>
        )}
      </div>
    </div>
  );
}