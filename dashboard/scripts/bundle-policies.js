const fs = require('fs')
const path = require('path')

// Function to format text with proper line breaks for readability
function formatTextWithLineBreaks(text) {
  if (!text || typeof text !== 'string') {
    return text
  }

  // Clean up the text first
  text = text.trim()

  // Add line breaks after sentences that end with periods followed by capital letters or numbers
  text = text.replace(/\. ([A-Z0-9])/g, '.\n\n$1')

  // Add line breaks after bullet points and dashes
  text = text.replace(/(- [A-Z])/g, '\n$1')
  text = text.replace(/(• [A-Z])/g, '\n$1')

  // Add line breaks after section headers (text followed by colon and capital letter)
  text = text.replace(/([A-Za-z\s]+:)\s*([A-Z])/g, '$1\n$2')

  // Add line breaks after numbered lists
  text = text.replace(/(\d+\.\s)/g, '\n$1')

  // Add line breaks before "**" headers
  text = text.replace(/([a-z])\s*(\*\*[A-Z])/g, '$1\n\n$2')

  // Clean up multiple consecutive line breaks
  text = text.replace(/\n{3,}/g, '\n\n')

  // Clean up leading/trailing whitespace
  text = text.trim()

  return text
}

// Path to the policies directory - try multiple locations
const possiblePolicyDirs = [
  path.join(__dirname, '..', '..', 'o'),           // Local development
  path.join(__dirname, '..', 'data', 'policies'), // Vercel build
  path.join(process.cwd(), 'o'),                   // Alternative path
]

let policiesDir = null
for (const dir of possiblePolicyDirs) {
  if (fs.existsSync(dir)) {
    policiesDir = dir
    break
  }
}

const outputFile = path.join(__dirname, '..', 'app', 'policies.json')
const publicOutputFile = path.join(__dirname, '..', 'public', 'policies.json')

// Ensure the data directories exist
const dataDir = path.dirname(outputFile)
const publicDir = path.dirname(publicOutputFile)
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true })
}
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true })
}

const policies = []

// Read all JSON files for policies 1-30
for (let i = 1; i <= 30; i++) {
  const filePath = path.join(policiesDir, `${i}.json`)
  if (fs.existsSync(filePath)) {
    try {
      const fileContent = fs.readFileSync(filePath, 'utf-8')
      const policy = JSON.parse(fileContent)

      // Format the description fields with proper line breaks
      if (policy.source_analysis) {
        if (policy.source_analysis.perplexity_description) {
          policy.source_analysis.perplexity_description = formatTextWithLineBreaks(policy.source_analysis.perplexity_description)
        }
        if (policy.source_analysis.rag_description) {
          policy.source_analysis.rag_description = formatTextWithLineBreaks(policy.source_analysis.rag_description)
        }
      }

      policies.push(policy)
    } catch (error) {
      console.error(`Error reading policy ${i}:`, error)
    }
  }
}

// Sort by policy_id
const sortedPolicies = policies.sort((a, b) => a.policy_id - b.policy_id)

// Write the bundled policies to both locations
const jsonContent = JSON.stringify(sortedPolicies, null, 2)
fs.writeFileSync(outputFile, jsonContent)
fs.writeFileSync(publicOutputFile, jsonContent)

console.log(`Bundled ${sortedPolicies.length} policies to ${outputFile}`)
console.log(`Also copied to ${publicOutputFile} for public access`)
