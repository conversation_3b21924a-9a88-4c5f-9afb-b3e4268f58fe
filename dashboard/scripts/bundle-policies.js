const fs = require('fs')
const path = require('path')

// Path to the policies directory - try multiple locations
const possiblePolicyDirs = [
  path.join(__dirname, '..', '..', 'o'),           // Local development
  path.join(__dirname, '..', 'data', 'policies'), // Vercel build
  path.join(process.cwd(), 'o'),                   // Alternative path
]

let policiesDir = null
for (const dir of possiblePolicyDirs) {
  if (fs.existsSync(dir)) {
    policiesDir = dir
    break
  }
}

const outputFile = path.join(__dirname, '..', 'app', 'policies.json')
const publicOutputFile = path.join(__dirname, '..', 'public', 'policies.json')

// Ensure the data directories exist
const dataDir = path.dirname(outputFile)
const publicDir = path.dirname(publicOutputFile)
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true })
}
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true })
}

const policies = []

// Read all JSON files for policies 1-30
for (let i = 1; i <= 30; i++) {
  const filePath = path.join(policiesDir, `${i}.json`)
  if (fs.existsSync(filePath)) {
    try {
      const fileContent = fs.readFileSync(filePath, 'utf-8')
      const policy = JSON.parse(fileContent)
      policies.push(policy)
    } catch (error) {
      console.error(`Error reading policy ${i}:`, error)
    }
  }
}

// Sort by policy_id
const sortedPolicies = policies.sort((a, b) => a.policy_id - b.policy_id)

// Write the bundled policies to both locations
const jsonContent = JSON.stringify(sortedPolicies, null, 2)
fs.writeFileSync(outputFile, jsonContent)
fs.writeFileSync(publicOutputFile, jsonContent)

console.log(`Bundled ${sortedPolicies.length} policies to ${outputFile}`)
console.log(`Also copied to ${publicOutputFile} for public access`)
