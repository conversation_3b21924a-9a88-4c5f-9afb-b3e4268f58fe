/** @type {import('next').NextConfig} */
const nextConfig = {
  // Ensure API routes are properly handled in production
  experimental: {
    serverComponentsExternalPackages: [],
    // Include the policies.json file in the API route bundle
    outputFileTracingIncludes: {
      '/api/policies': ['./app/policies.json']
    }
  },
  // Ensure static assets are properly bundled
  webpack: (config) => {
    // Ensure JSON files are properly handled
    config.module.rules.push({
      test: /\.json$/,
      type: 'json'
    });

    return config;
  }
}

module.exports = nextConfig
